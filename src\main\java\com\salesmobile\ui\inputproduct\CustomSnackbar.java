package com.salesmobile.ui.inputproduct;

import android.content.Context;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.salesmobile.R;

public class CustomSnackbar {

    public static void show(Context context, String message) {
        // Inflar el layout de Snackbar personalizado
        View view = LayoutInflater.from(context).inflate(R.layout.custom_snackbar_layout, null);

        // Configurar el mensaje
        TextView textViewMessage = view.findViewById(R.id.message);
        textViewMessage.setText(message);

        // Crear un Toast con vista personalizada
        Toast toast = new Toast(context);
        toast.setDuration(Toast.LENGTH_SHORT);
        toast.setView(view);
        toast.show();

        // Cerrar el Snackbar después de 2 segundos
        Handler handler = new Handler();
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                toast.cancel();
            }
        }, 2000);
    }
}