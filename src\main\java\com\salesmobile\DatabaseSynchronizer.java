package com.salesmobile;

import android.content.ContentValues;
import android.content.Context;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.AsyncTask;
import android.util.Log;

import android.widget.Toast;

import com.salesmobile.config.ParametrosConf;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.concurrent.CountDownLatch;

public class DatabaseSynchronizer {
    private static final String TAG = "DatabaseSynchronizer";
    private final Context context;
    private final SharedPreferences sharedPreferences;
    private final com.salesmobile.config.SharedPreferences customSharedPreferences;
    private final DatabaseHelper dbHelper;
    private SQLiteDatabase localDb;
    private final TokenManager2 tokenManager2;
    private Integer idPuntoVentaSeleccionado = null;

    public interface SyncListener {
        void onSyncComplete(boolean success, String message);
    }

    public DatabaseSynchronizer(Context context, android.content.SharedPreferences sharedPreferences) {
        this.context = context.getApplicationContext();
        this.sharedPreferences = sharedPreferences;
        this.customSharedPreferences = new com.salesmobile.config.SharedPreferences(context);
        this.dbHelper = new DatabaseHelper(context);
        this.tokenManager2 = TokenManager2.getInstance();
    }

    public void startSyncWithExistingConnection(Connection connection, SyncListener listener) {
        new SyncTask(connection, listener).execute();
    }

    private class SyncTask extends AsyncTask<Void, Void, SyncResult> {
        private final Connection connection;
        private final SyncListener listener;
        private final CountDownLatch tokenLatch = new CountDownLatch(1);
        private String authToken;


        SyncTask(Connection connection, SyncListener listener) {
            this.connection = connection;
            this.listener = listener;
        }

        @Override
        protected SyncResult doInBackground(Void... voids) {
            try {
                localDb = dbHelper.getWritableDatabase();
                localDb.beginTransaction();

                int pvCount = syncPuntosDeVenta(connection);
                int configCount = syncConfiguraciones(connection);
                int vendCount = syncVendedores(connection);
                int paramCount = syncParametros(connection);

                localDb.setTransactionSuccessful();

                // Actualizar ParametrosConf con las configuraciones sincronizadas
                ParametrosConf.refresh();

                // Mover la sincronización de índices al final del proceso

                return new SyncResult(true,
                        "Tablas sincronizadas: Config(" + configCount + "), PV(" + pvCount + "), Vend(" + vendCount + ") , Params(" + paramCount + ")");

            } catch (Exception e) {
                Log.e(TAG, "Error en sincronización: " + e.getMessage());
                return new SyncResult(false, "Error: " + e.getMessage());
            } finally {
                if (localDb != null) {
                    localDb.endTransaction();
                    localDb.close();
                }
            }
        }


        @Override
        protected void onPostExecute(SyncResult result) {
            // NUEVO: Transferir datos a Room Database después de sincronización exitosa
            if (result.success) {
                // Ejecutar transferencia y sincronización de índices en un hilo de background
                new Thread(() -> {
                    transferDataToRoomDatabaseSync();

                    // Sincronizar índices después de que todo esté listo
                    TokenManager2 tokenManager2 = TokenManager2.getInstance();
                    tokenManager2.requestToken(context, () -> {
                        String newToken = tokenManager2.getToken();
                        if (newToken != null && !newToken.isEmpty()) {
                            syncIndices(newToken);
                        } else {
                            Toast.makeText(context, "Error al obtener el token", Toast.LENGTH_LONG).show();
                        }
                    });
                }).start();
            }

            if (listener != null) {
                listener.onSyncComplete(result.success, result.message);
            }
        }
    }


    private int syncPuntosDeVenta(Connection connection) throws SQLException {
        if (!tablaExiste(DatabaseHelper.TABLE_PUNTOS_VENTA)) {
            crearTablaPuntosDeVenta();
        }

        localDb.delete(DatabaseHelper.TABLE_PUNTOS_VENTA, null, null);

        String nombre = sharedPreferences.getString("puntoventa", "");
        String query = "SELECT id, nombre, prefijo FROM puntosdeventas" +
                (nombre.isEmpty() ? "" : " WHERE nombre = '" + nombre + "'");
        //Log.d(TAG, "PUNTOS DE VENTA Query SQL: " + query);
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(query)) {
            int count = 0;
            java.util.List<com.salesmobile.config.SharedPreferences.PuntoVentaSincronizado> puntosVenta = new java.util.ArrayList<>();

            while (rs.next()) {
                ContentValues values = new ContentValues();
                int id = rs.getInt("id");
                String nombrePV = rs.getString("nombre") != null ? rs.getString("nombre") : "";
                String prefijo = rs.getString("prefijo") != null ? rs.getString("prefijo") : "";

                //Log.d(TAG, "Resultado encontrado - ID: " + id + ", Nombre: " + nombrePV + ", Prefijo: " + prefijo);

                values.put(DatabaseHelper.PV_ID, id);
                values.put(DatabaseHelper.PV_NOMBRE, nombrePV);
                values.put(DatabaseHelper.PV_PREFIJO, prefijo);
                localDb.insert(DatabaseHelper.TABLE_PUNTOS_VENTA, null, values);
                count++;

                com.salesmobile.config.SharedPreferences.PuntoVentaSincronizado pv = new com.salesmobile.config.SharedPreferences.PuntoVentaSincronizado(
                    id, nombrePV, prefijo
                );
                puntosVenta.add(pv);

                if (!nombre.isEmpty()) {
                    //Log.d(TAG, "Guardando punto de venta seleccionado - ID: " + id + ", Nombre: " + nombrePV);
                    SharedPreferences.Editor editor = sharedPreferences.edit();
                    editor.putInt("id_puntoventa", id);
                    editor.putString("nombre_puntoventa", nombrePV);
                    editor.putString("prefijo_puntoventa", prefijo);
                    editor.apply();

                    idPuntoVentaSeleccionado = id;
                    //Log.d(TAG, "idPuntoVentaSeleccionado establecido a: " + idPuntoVentaSeleccionado);
                } else {
                    Log.w(TAG, "Nombre punto venta vacío, no se selecciona como punto de venta activo");
                }
            }

            if (count > 0) {
                customSharedPreferences.saveSyncedPuntosVenta(puntosVenta);
                Log.d(TAG, "Puntos de venta guardados en SharedPreferences");
            }

            Log.d(TAG, "Total puntos de venta sincronizados: " + count);
            return count;
        }
    }

    private int syncConfiguraciones(Connection connection) throws SQLException {
        Log.d(TAG, "=== INICIO SYNC CONFIGURACIONES ===");

        localDb.delete(DatabaseHelper.TABLE_CONFIGURACIONES, null, null);

        String query = "SELECT id, urlapi, usuario, clave, grandtype, codempresa, codsucursal, urlapi2, usuario2, clave2, grandtype2, codempresa2, codsucursal2, id_puntoventa FROM configuraciones";
       // Log.d(TAG, "Query SQL: " + query);
        
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(query)) {

            int count = 0;
            java.util.List<com.salesmobile.config.SharedPreferences.ConfiguracionSincronizada> configuraciones = new java.util.ArrayList<>();

            while (rs.next()) {
                int id = rs.getInt("id");
                String urlapi = rs.getString("urlapi") != null ? rs.getString("urlapi").trim() : "";
                String usuario = rs.getString("usuario") != null ? rs.getString("usuario").trim() : "";
                String clave = rs.getString("clave") != null ? rs.getString("clave").trim() : "";
                String granttype = rs.getString("grandtype") != null ? rs.getString("grandtype").trim() : "";
                String codempresa = rs.getString("codempresa") != null ? rs.getString("codempresa").trim() : "";
                String codsucursal = rs.getString("codsucursal") != null ? rs.getString("codsucursal").trim() : "";
                String urlapi2 = rs.getString("urlapi2") != null ? rs.getString("urlapi2").trim() : "";
                String usuario2 = rs.getString("usuario2") != null ? rs.getString("usuario2").trim() : "";
                String clave2 = rs.getString("clave2") != null ? rs.getString("clave2").trim() : "";
                String granttype2 = rs.getString("grandtype2") != null ? rs.getString("grandtype2").trim() : "";
                String codempresa2 = rs.getString("codempresa2") != null ? rs.getString("codempresa2").trim() : "";
                String codsucursal2 = rs.getString("codsucursal2") != null ? rs.getString("codsucursal2").trim() : "";
                int idPuntoventa = rs.getInt("id_puntoventa");

                ContentValues values = new ContentValues();
                values.put(DatabaseHelper.CONFIG_ID, id);
                values.put(DatabaseHelper.CONFIG_URLAPI, urlapi);
                values.put(DatabaseHelper.CONFIG_USUARIO, usuario);
                values.put(DatabaseHelper.CONFIG_CLAVE, clave);
                values.put(DatabaseHelper.CONFIG_GRANTTYPE, granttype);
                values.put(DatabaseHelper.CONFIG_CODEMPRESA, codempresa);
                values.put(DatabaseHelper.CONFIG_CODSUCURSAL, codsucursal);
                values.put(DatabaseHelper.CONFIG_URLAPI2, urlapi2);
                values.put(DatabaseHelper.CONFIG_USUARIO2, usuario2);
                values.put(DatabaseHelper.CONFIG_CLAVE2, clave2);
                values.put(DatabaseHelper.CONFIG_GRANTTYPE2, granttype2);
                values.put(DatabaseHelper.CONFIG_CODEMPRESA2, codempresa2);
                values.put(DatabaseHelper.CONFIG_CODSUCURSAL2, codsucursal2);
                values.put(DatabaseHelper.CONFIG_ID_PUNTOVENTA, idPuntoventa);

                long insertResult = localDb.insert(DatabaseHelper.TABLE_CONFIGURACIONES, null, values);

                com.salesmobile.config.SharedPreferences.ConfiguracionSincronizada config = new com.salesmobile.config.SharedPreferences.ConfiguracionSincronizada(
                    id, urlapi, usuario, clave, granttype, codempresa, codsucursal,
                    urlapi2, usuario2, clave2, granttype2, codempresa2, codsucursal2, idPuntoventa
                );
                configuraciones.add(config);

                count++;
            }

            if (count > 0) {
                customSharedPreferences.saveSyncedConfiguraciones(configuraciones);
                Log.d(TAG, "Configuraciones guardadas en SharedPreferences");
            }

            Log.d(TAG, "Total configuraciones sincronizadas: " + count);
            return count;
        } catch (SQLException e) {
            Log.e(TAG, "Error en syncConfiguraciones: " + e.getMessage());
            throw e;
        }
    }

    // Sincronizar Parametros
    private int syncParametros(Connection connection) throws SQLException {

        if (!tablaExiste(DatabaseHelper.TABLE_PARAMETROS)) {
            crearTablaParametros();
        } else if (!columnaExiste(DatabaseHelper.TABLE_PARAMETROS, DatabaseHelper.VER_CONFIG)) {
            // Si la tabla existe pero no tiene la columna ver_config, recrearla
            localDb.execSQL("DROP TABLE IF EXISTS " + DatabaseHelper.TABLE_PARAMETROS);
            crearTablaParametros();
        }

        localDb.delete(DatabaseHelper.TABLE_PARAMETROS, null, null);

        String query = "SELECT * FROM parametros";

        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(query)) {

            int count = 0;
            while (rs.next()) {
                ContentValues values = new ContentValues();
                String test = rs.getString("TEST");
                String testPc = rs.getString("TESTPC");
                String entorno = rs.getString("ENTORNO");
                String devBancardUrl = rs.getString("DEV_BANCARD_URL");
                String devBancardPublicKey = rs.getString("DEV_BANCARD_PUBLIC_KEY");
                String devBancardPrivateKey = rs.getString("DEV_BANCARD_PRIVATE_KEY");
                String prodBancardUrl = rs.getString("PROD_BANCARD_URL");
                String prodBancardPublicKey = rs.getString("PROD_BANCARD_PUBLIC_KEY");
                String prodBancardPrivateKey = rs.getString("PROD_BANCARD_PRIVATE_KEY");
                String devDefaultUrl = rs.getString("DEV_DEFAULT_URL");
                String prodDefaultUrl = rs.getString("PROD_DEFAULT_URL");
                int bancardCommerceCode = rs.getInt("BANCARD_COMMERCE_CODE");
                int bancardCommerceBranch = rs.getInt("BANCARD_COMMERCE_BRANCH");
                String appmovilDefaultUrl = rs.getString("APPMOVIL_DEFAULT_URL");
                int verConfig = rs.getInt("VER_CONFIG");

                values.put(DatabaseHelper.TEST, test);
                values.put(DatabaseHelper.TESTPC, testPc);
                values.put(DatabaseHelper.ENTORNO, entorno);
                values.put(DatabaseHelper.DEV_BANCARD_URL, devBancardUrl);
                values.put(DatabaseHelper.DEV_BANCARD_PUBLIC_KEY, devBancardPublicKey);
                values.put(DatabaseHelper.DEV_BANCARD_PRIVATE_KEY, devBancardPrivateKey);
                values.put(DatabaseHelper.PROD_BANCARD_URL, prodBancardUrl);
                values.put(DatabaseHelper.PROD_BANCARD_PUBLIC_KEY, prodBancardPublicKey);
                values.put(DatabaseHelper.PROD_BANCARD_PRIVATE_KEY, prodBancardPrivateKey);
                values.put(DatabaseHelper.DEV_DEFAULT_URL, devDefaultUrl);
                values.put(DatabaseHelper.PROD_DEFAULT_URL, prodDefaultUrl);
                values.put(DatabaseHelper.BANCARD_COMMERCE_CODE, bancardCommerceCode);
                values.put(DatabaseHelper.BANCARD_COMMERCE_BRANCH, bancardCommerceBranch);
                values.put(DatabaseHelper.APPMOVIL_DEFAULT_URL, appmovilDefaultUrl);
                values.put(DatabaseHelper.VER_CONFIG, verConfig);
                values.put(DatabaseHelper.ID_PARAMETROS, rs.getInt("ID_PARAMETROS"));

                long insertResult = localDb.insert(DatabaseHelper.TABLE_PARAMETROS, null, values);

                // Usar saveParametrosConLogica para aplicar lógica de ParametrosConf y evitar inconsistencias
                customSharedPreferences.saveParametrosConLogica(
                    test != null ? test : "",
                    testPc != null ? testPc : "",
                    entorno != null ? entorno : "",
                    devBancardUrl != null ? devBancardUrl : "",
                    devBancardPublicKey != null ? devBancardPublicKey : "",
                    devBancardPrivateKey != null ? devBancardPrivateKey : "",
                    prodBancardUrl != null ? prodBancardUrl : "",
                    prodBancardPublicKey != null ? prodBancardPublicKey : "",
                    prodBancardPrivateKey != null ? prodBancardPrivateKey : "",
                    devDefaultUrl != null ? devDefaultUrl : "",
                    prodDefaultUrl != null ? prodDefaultUrl : "",
                    bancardCommerceCode,
                    bancardCommerceBranch,
                    appmovilDefaultUrl != null ? appmovilDefaultUrl : "",
                    verConfig
                );

                count++;
            }
            Log.d(TAG, "Total parametros sincronizados: " + count);
            if (count > 0) {
                Log.d(TAG, "Parametros guardados en SharedPreferences");
            }
            return count;
        }
        }


    private int syncVendedores(Connection connection) throws SQLException {

        if (!tablaExiste(DatabaseHelper.TABLE_VENDEDORES)) {
            crearTablaVendedores();
        }


        localDb.delete(DatabaseHelper.TABLE_VENDEDORES, null, null);

        String usuarioSQL = sharedPreferences.getString("usuarioSQL", "");
        String query = "SELECT * FROM vendedores" +
                (usuarioSQL.isEmpty() ? "" : " WHERE usuariosql = '" + usuarioSQL + "'");

        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(query)) {

            int count = 0;
            java.util.List<com.salesmobile.config.SharedPreferences.VendedorSincronizado> vendedores = new java.util.ArrayList<>();

            while (rs.next()) {
                String codigo = rs.getString("codigo") != null ? rs.getString("codigo") : "";
                String nombre = rs.getString("nombre") != null ? rs.getString("nombre") : "";
                String soloconsulta = rs.getString("soloconsulta") != null ? rs.getString("soloconsulta") : "N";

                ContentValues values = new ContentValues();
                values.put(DatabaseHelper.VEND_CODIGO, codigo);

                localDb.insert(DatabaseHelper.TABLE_VENDEDORES, null, values);
                count++;

                SharedPreferences.Editor editor = sharedPreferences.edit();
                editor.putString("codigovendedor", codigo);
                editor.putString("soloconsulta", soloconsulta);
                editor.apply();

                com.salesmobile.config.SharedPreferences.VendedorSincronizado vendedor = new com.salesmobile.config.SharedPreferences.VendedorSincronizado(
                    codigo, nombre, soloconsulta
                );
                vendedores.add(vendedor);
            }

            if (count > 0) {
                customSharedPreferences.saveSyncedVendedores(vendedores);
                Log.d(TAG, "Vendedores guardados en SharedPreferences");
            }

            return count;
        }
    }


    private static class SyncResult {
        final boolean success;
        final String message;

        SyncResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
    }

    private boolean tablaExiste(String tableName) {
        try (Cursor cursor = localDb.rawQuery(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                new String[]{tableName})) {
            return cursor != null && cursor.getCount() > 0;
        }
    }

    private boolean columnaExiste(String tableName, String columnName) {
        try (Cursor cursor = localDb.rawQuery("PRAGMA table_info(" + tableName + ")", null)) {
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    int nameIndex = cursor.getColumnIndex("name");
                    String name = (nameIndex >= 0) ? cursor.getString(nameIndex) : null;
                    if (columnName.equals(name)) {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    private void crearTablaPuntosDeVenta() {
        localDb.execSQL("CREATE TABLE " + DatabaseHelper.TABLE_PUNTOS_VENTA + " (" +
                DatabaseHelper.PV_NOMBRE + " TEXT, " +
                DatabaseHelper.PV_PREFIJO + " TEXT PRIMARY KEY)");
    }

    private void crearTablaVendedores() {
        localDb.execSQL("CREATE TABLE " + DatabaseHelper.TABLE_VENDEDORES + " (" +
                DatabaseHelper.VEND_CODIGO + " TEXT, " +
                DatabaseHelper.VEND_NOMBRE + " TEXT, " +
                DatabaseHelper.VEND_USUARIOSQL + " TEXT, " +
                DatabaseHelper.VEND_SOLOCONSULTA + " TEXT)");
    }

    private void crearTablaParametros() {
        localDb.execSQL("CREATE TABLE " + DatabaseHelper.TABLE_PARAMETROS + " (" +
                DatabaseHelper.TEST + " TEXT, " +
                DatabaseHelper.TESTPC + " TEXT, " +
                DatabaseHelper.ENTORNO + " TEXT, " +
                DatabaseHelper.DEV_BANCARD_URL + " TEXT, " +
                DatabaseHelper.DEV_BANCARD_PUBLIC_KEY + " TEXT, " +
                DatabaseHelper.DEV_BANCARD_PRIVATE_KEY + " TEXT, " +
                DatabaseHelper.PROD_BANCARD_URL + " TEXT, " +
                DatabaseHelper.PROD_BANCARD_PUBLIC_KEY + " TEXT, " +
                DatabaseHelper.PROD_BANCARD_PRIVATE_KEY + " TEXT, " +
                DatabaseHelper.DEV_DEFAULT_URL + " TEXT, " +
                DatabaseHelper.PROD_DEFAULT_URL + " TEXT, " +
                DatabaseHelper.BANCARD_COMMERCE_CODE + " INTEGER, " +
                DatabaseHelper.BANCARD_COMMERCE_BRANCH + " INTEGER, " +
                DatabaseHelper.APPMOVIL_DEFAULT_URL + " TEXT, " +
                DatabaseHelper.VER_CONFIG + " INTEGER, " +
                DatabaseHelper.ID_PARAMETROS + " INTEGER PRIMARY KEY)");
    }

    private void syncIndices(String token) {
        IndicesApiClient apiClient = new IndicesApiClient(context);
        apiClient.fetchIndicesAndSaveValor1(token, new IndicesApiClient.IndicesCallback() {
            @Override
            public void onSuccess(double valor1) {
                Log.i(TAG, "Índices sincronizados. Valor1: " + valor1);
            }

            @Override
            public void onError(String message) {
                Log.e(TAG, "Error en índices: " + message);
            }
        });
    }


    private void transferDataToRoomDatabaseSync() {
        Log.d(TAG, "=== INICIANDO TRANSFERENCIA A ROOM DATABASE ===");
        
        try {
            AppDatabase roomDb = AppDatabase.getInstance(context);
            DatabaseHelper sqliteHelper = new DatabaseHelper(context);
            SQLiteDatabase sqliteDb = sqliteHelper.getReadableDatabase();
            
            // Consultar configuraciones desde SQLite
            Cursor cursor = sqliteDb.query(
                DatabaseHelper.TABLE_CONFIGURACIONES,
                null, // todas las columnas
                null, null, null, null, null
            );
            
            if (cursor != null && cursor.moveToFirst()) {
                Log.d(TAG, "Encontradas " + cursor.getCount() + " configuraciones en SQLite");
                
                do {
                    // Crear objeto Configuracion para Room
                    Configuracion config = new Configuracion();
                    config.setId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_ID)));
                    config.setBaseUrl(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_URLAPI)));
                    config.setUsuario(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_USUARIO)));
                    config.setContrasena(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_CLAVE)));
                    config.setGrantType(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_GRANTTYPE)));
                    config.setCodigoEmpresa(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_CODEMPRESA)));
                    config.setCodigoSucursal(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_CODSUCURSAL)));
                    config.setBaseUrl2(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_URLAPI2)));
                    config.setUsuario2(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_USUARIO2)));
                    config.setContrasena2(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_CLAVE2)));
                    config.setGrantType2(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_GRANTTYPE2)));
                    config.setCodigoEmpresa2(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_CODEMPRESA2)));
                    config.setCodigoSucursal2(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_CODSUCURSAL2)));
                    config.setIdPuntoventa(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.CONFIG_ID_PUNTOVENTA)));
                    
                    // Insertar en Room Database (síncrono)
                    try {
                        roomDb.configuracionDao().insert(config);
                        Log.d(TAG, "Configuración transferida exitosamente a Room: ID=" + config.getId());
                    } catch (Exception e) {
                        Log.e(TAG, "Error insertando en Room: " + e.getMessage());
                    }
                    
                } while (cursor.moveToNext());
                
                cursor.close();
            } else {
                Log.w(TAG, "No se encontraron configuraciones en SQLite para transferir");
            }
            
            sqliteDb.close();
            
        } catch (Exception e) {
            Log.e(TAG, "Error en transferDataToRoomDatabase: " + e.getMessage());
        }
    }


}