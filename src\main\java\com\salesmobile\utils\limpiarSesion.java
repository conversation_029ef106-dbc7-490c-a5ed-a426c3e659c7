package com.salesmobile.utils;

import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.util.Log;
import com.salesmobile.AppDatabase;
import com.salesmobile.config.SharedPreferences;
import com.salesmobile.ui.login.LoginActivity;
import java.io.File;
import java.util.concurrent.atomic.AtomicBoolean;

public class limpiarSesion {
    private static final String TAG = "limpiarSesion";
    private static limpiarSesion instance;
    private final AtomicBoolean isCleanupInProgress = new AtomicBoolean(false);
    
    public interface CleanupCallback {
        void onCleanupComplete(boolean success, String message);
        void onCleanupProgress(String operation);
    }
    
    public static synchronized limpiarSesion getInstance() {
        if (instance == null) {
            instance = new limpiarSesion();
        }
        return instance;
    }
    
    private limpiarSesion() {}
    
    public void performLogout(Context context, CleanupCallback callback) {
        if (isCleanupInProgress.get()) {
            Log.w(TAG, "Cleanup already in progress, ignoring request");
            if (callback != null) {
                callback.onCleanupComplete(false, "Cleanup already in progress");
            }
            return;
        }
        
        isCleanupInProgress.set(true);
        Log.i(TAG, "Starting session cleanup process");
        
        new CleanupTask(context, callback).execute();
    }
    
    private class CleanupTask extends AsyncTask<Void, String, Boolean> {
        private final Context context;
        private final CleanupCallback callback;
        private String errorMessage = "";
        
        CleanupTask(Context context, CleanupCallback callback) {
            this.context = context.getApplicationContext();
            this.callback = callback;
        }
        
        @Override
        protected void onProgressUpdate(String... values) {
            if (callback != null && values.length > 0) {
                callback.onCleanupProgress(values[0]);
            }
        }
        
        @Override
        protected Boolean doInBackground(Void... voids) {
            try {
                boolean[] cleanupResults = new boolean[4];
                
                publishProgress("Cancelling background processes...");
                cleanupResults[0] = cancelBackgroundProcesses();
                
                publishProgress("Clearing temporary files...");
                cleanupResults[1] = clearTemporaryFiles(context);
                
                // No resetear la base
                // publishProgress("Resetting database...");
                // cleanupResults[2] = resetDatabase(context);
                
                publishProgress("Clearing session data...");
                cleanupResults[2] = clearSessionData(context);

                publishProgress("Validating cleanup...");
                cleanupResults[3] = validateCleanup(context);
                
                boolean allSuccess = true;
                for (boolean result : cleanupResults) {
                    if (!result) {
                        allSuccess = false;
                        break;
                    }
                }
                
                return allSuccess;
                
            } catch (Exception e) {
                Log.e(TAG, "Error during cleanup: " + e.getMessage(), e);
                errorMessage = "Cleanup failed: " + e.getMessage();
                return false;
            }
        }
        
        @Override
        protected void onPostExecute(Boolean success) {
            isCleanupInProgress.set(false);
            
            if (success) {
                Log.i(TAG, "Session cleanup completed successfully");
                navigateToLogin(context);
                if (callback != null) {
                    callback.onCleanupComplete(true, "Cleanup completed successfully");
                }
            } else {
                Log.e(TAG, "Session cleanup failed: " + errorMessage);
                if (callback != null) {
                    callback.onCleanupComplete(false, errorMessage.isEmpty() ? "Cleanup failed" : errorMessage);
                }
            }
        }
    }
    
    private boolean cancelBackgroundProcesses() {
        try {
            Log.d(TAG, "Cancelling background processes");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error cancelling background processes: " + e.getMessage(), e);
            return false;
        }
    }
    
    private boolean clearTemporaryFiles(Context context) {
        try {
            Log.d(TAG, "Clearing temporary files");
            
            File cacheDir = context.getCacheDir();
            if (cacheDir != null && cacheDir.exists()) {
                deleteRecursively(cacheDir);
            }
            
            File tempDir = new File(context.getCacheDir(), "temp");
            if (tempDir.exists()) {
                deleteRecursively(tempDir);
            }
            
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error clearing temporary files: " + e.getMessage(), e);
            return false;
        }
    }
    
    private boolean resetDatabase(Context context) {
        try {
            Log.d(TAG, "Resetting database");
            
            AppDatabase db = AppDatabase.getInstance(context);
            if (db != null) {
                /*
                new Thread(() -> {

                    try {
                        db.ventaDao().deleteAll();
                        db.configuracionDao().deleteAll();
                        db.puntosdeventasDao().deleteAll();
                    } catch (Exception e) {
                        Log.e(TAG, "Error clearing database tables: " + e.getMessage(), e);
                    }

                }).start();
                */
            }
            
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error resetting database: " + e.getMessage(), e);
            return false;
        }
    }
    
    private boolean clearSessionData(Context context) {
        try {
            Log.d(TAG, "Clearing session data");
            
            SharedPreferences sharedPrefs = new SharedPreferences(context);
            sharedPrefs.clearSessionData();
            
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error clearing session data: " + e.getMessage(), e);
            return false;
        }
    }
    
    private boolean validateCleanup(Context context) {
        try {
            Log.d(TAG, "Validating cleanup");
            
            SharedPreferences sharedPrefs = new SharedPreferences(context);
            boolean hasValidSession = sharedPrefs.hasValidSession();
            boolean hasSyncedData = sharedPrefs.hasSyncedData();
            
            if (hasValidSession || hasSyncedData) {
                Log.w(TAG, "Cleanup validation failed - session data still present");
                return false;
            }
            
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error validating cleanup: " + e.getMessage(), e);
            return false;
        }
    }
    
    private void deleteRecursively(File file) {
        if (file.isDirectory()) {
            File[] children = file.listFiles();
            if (children != null) {
                for (File child : children) {
                    deleteRecursively(child);
                }
            }
        }
        
        if (file.exists() && !file.delete()) {
            Log.w(TAG, "Failed to delete file: " + file.getAbsolutePath());
        }
    }
    
    private void navigateToLogin(Context context) {
        try {
            Intent loginIntent = new Intent(context, LoginActivity.class);
            loginIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            context.startActivity(loginIntent);
            Log.i(TAG, "Navigation to LoginActivity initiated");
        } catch (Exception e) {
            Log.e(TAG, "Error navigating to login: " + e.getMessage(), e);
        }
    }
}
