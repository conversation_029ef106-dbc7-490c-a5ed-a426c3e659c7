package com.salesmobile;

import android.content.Context;
import android.os.AsyncTask;
import android.util.Log;

import com.salesmobile.config.ParametrosConf;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;

import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class TokenRequest2 extends AsyncTask<Void, Void, String> {

    private Context context; // Contexto para acceder a la base de datos
    private TokenRequestListener listener; // Listener para manejar el token

    // Constructor para establecer el listener y el contexto
    public TokenRequest2(Context context, TokenRequestListener listener) {
        this.context = context;
        this.listener = listener;
    }


    @Override
    protected String doInBackground(Void... voids) {
        // Obtener la configuración desde la base de datos
        AppDatabase db = AppDatabase.getInstance(context);
        Configuracion configuracion = db.configuracionDao().obtenerConfiguracion();

        if (configuracion == null) {
            Log.e("TokenRequest 2", "No se encontró la configuración en la base de datos");
            return null;
        }

        // Usar los valores de la configuración
        String baseUrl = configuracion.getBaseUrl2();
        String username = configuracion.getUsuario2();
        String password = configuracion.getContrasena2();
        String grantType = configuracion.getGrantType2();

        OkHttpClient client = new OkHttpClient();

        RequestBody formBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("grant_type", grantType)
                .addFormDataPart("client_id", "api")
                .addFormDataPart("client_secret", "secret")
                .addFormDataPart("refresh_token", "")
                .addFormDataPart("username", username)
                .addFormDataPart("password", password)
                .build();


        String fullUrl = ParametrosConf.API2_URL + "/auth/token";
        Request request = new Request.Builder()
                .url(fullUrl)
                .header("accept", "text/plain")
                .post(formBody)
                .build();

        // Ejecutar la solicitud y obtener la respuesta
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            return responseBody;
        } catch (IOException e) {
            Log.e("TokenRequest2", "Error en la petición HTTP: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    @Override
    protected void onPostExecute(String jsonResult) {
        if (jsonResult != null) {

            try {
                // Parsear el JSON para obtener el token
                JSONObject jsonObject = new JSONObject(jsonResult);
                String accessToken = jsonObject.getString("access_token");
                Log.d("TokenRequest2", "Token obtenido exitosamente");

                // Llamar al listener con el token
                if (listener != null) {
                    listener.onTokenReceived(accessToken);
                }
            } catch (JSONException e) {
                Log.e("TokenRequest2", "Error parsing JSON: " + e.getMessage());
                Log.e("TokenRequest2", "JSON que causó el error: " + jsonResult);
                e.printStackTrace();
                if (listener != null) {
                    listener.onTokenReceived(null);
                }
            }
        } else {
            Log.e("TokenRequest2", "No se recibió respuesta del servidor");
            // Si no se obtuvo respuesta, llamar al listener con null
            if (listener != null) {
                listener.onTokenReceived(null);
            }
        }
    }

    // Interfaz de devolución de llamada para manejar el token
    public interface TokenRequestListener {
        void onTokenReceived(String token);
    }
}