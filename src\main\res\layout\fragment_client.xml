<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/white"
    tools:context=".ui.client.ClientFragment">

    <!-- Número de Documento -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/textView3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.4"
            android:text="Nro. Documento:"
            android:textColor="@color/purple_700"
            android:textSize="16sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/editNroDocumento"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.6"
            android:ems="10"
            android:inputType="text"
            android:background="@drawable/edittext_background"
            android:padding="12dp"
            android:textColor="@color/black"/>
    </LinearLayout>

    <!-- Nombre del Cliente -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/textNombreClient"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.4"
            android:text="Nombre Cliente:"
            android:textColor="@color/purple_700"
            android:textSize="16sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/editNombreClient"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.6"
            android:ems="20"
            android:inputType="textCapWords|textMultiLine"
            android:maxLength="100"
            android:background="@drawable/edittext_background"
            android:padding="12dp"
            android:textColor="@color/black"/>
    </LinearLayout>

    <!-- Email -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/textEmail"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.4"
            android:text="e-Mail:"
            android:textColor="@color/purple_700"
            android:textSize="16sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/editEmail"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.6"
            android:ems="10"
            android:inputType="textEmailAddress"
            android:background="@drawable/edittext_background"
            android:padding="12dp"
            android:textColor="@color/black"/>
    </LinearLayout>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBarCliente"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="24dp"
        android:indeterminateTint="@color/purple_500"
        android:visibility="gone"/>

    <!-- Botones -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="8dp">

        <Button
            android:id="@+id/btnGuardar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="Guardar"
            android:backgroundTint="@color/purple_500"
            android:textColor="@color/white"
            android:paddingVertical="12dp"
            android:textAllCaps="false"
            style="@style/Widget.MaterialComponents.Button"/>

        <Button
            android:id="@+id/btnCancelar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="Cancelar"
            android:backgroundTint="@color/grey"
            android:textColor="@color/white"
            android:paddingVertical="12dp"
            android:textAllCaps="false"
            style="@style/Widget.MaterialComponents.Button"/>
    </LinearLayout>

</LinearLayout>