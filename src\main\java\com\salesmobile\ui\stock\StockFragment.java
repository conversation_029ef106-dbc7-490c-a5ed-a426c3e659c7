package com.salesmobile.ui.stock;

import com.salesmobile.config.ParametrosConf;
import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.GridLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.salesmobile.AppDatabase;
import com.salesmobile.Configuracion;
import com.salesmobile.R;
import com.salesmobile.TokenManager;
import com.salesmobile.TokenManager2;
import com.salesmobile.TokenRequest;
import com.salesmobile.ui.inputproduct.SharedViewModel;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.MediaType;



public class StockFragment extends Fragment {
    private EditText editTextProductCode;
    private Button buttonCheckStock;
    private RecyclerView recyclerViewStock;
    private StockAdapter adapter;
    private ProgressBar progressBarStock;
    private String baseUrl, codEmpresa, codSucursal, fechaFormateada;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_stock, container, false);

        editTextProductCode = view.findViewById(R.id.editTextProductCode);
        buttonCheckStock = view.findViewById(R.id.buttonCheckStock);
        recyclerViewStock = view.findViewById(R.id.recyclerViewStock);
        progressBarStock = view.findViewById(R.id.progressBarStock);

        setupRecyclerView();
        obtenerConfiguracion();

        buttonCheckStock.setOnClickListener(v -> buscarStock());

        return view;
    }

    private void setupRecyclerView() {
        recyclerViewStock.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerViewStock.setBackgroundColor(Color.LTGRAY);
        adapter = new StockAdapter();
        recyclerViewStock.setAdapter(adapter);

        DividerItemDecoration divider = new DividerItemDecoration(getContext(), DividerItemDecoration.VERTICAL);
        recyclerViewStock.addItemDecoration(divider);
    }

    private void obtenerConfiguracion() {
        Executors.newSingleThreadExecutor().execute(() -> {
            AppDatabase db = AppDatabase.getInstance(requireContext());
            Configuracion configuracion = db.configuracionDao().obtenerConfiguracion();

            requireActivity().runOnUiThread(() -> {
                baseUrl = configuracion != null ? configuracion.getBaseUrl2() : ParametrosConf.APPMOVIL_BASE_URL;
                codEmpresa = configuracion != null ? configuracion.getCodigoEmpresa2() : "1";
                codSucursal = configuracion != null ? configuracion.getCodigoSucursal2() : "1";
                fechaFormateada = LocalDate.now().format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
            });
        });
    }

    private void buscarStock() {
        String codigoProducto = editTextProductCode.getText().toString().trim();
        if (codigoProducto.isEmpty()) {
            Toast.makeText(getContext(), "Ingrese un código de producto", Toast.LENGTH_SHORT).show();
            return;
        }

        TokenManager2 tokenManager2 = TokenManager2.getInstance();
        String token = tokenManager2.getToken();

        //if (token != null && !token.isEmpty()) {
        //    consultarStockAPI(token, codigoProducto);
        //} else {
            tokenManager2.requestToken(requireContext(), () -> {
                String newToken = tokenManager2.getToken();
                if (newToken != null && !newToken.isEmpty()) {
                    consultarStockAPI(newToken, codigoProducto);
                } else {
                    mostrarError("Error al obtener token de acceso");
                }
            });
        //}
    }

    private void consultarStockAPI(String token, String codigoProducto) {
        progressBarStock.setVisibility(View.VISIBLE);

        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();

        RequestBody requestBody = crearRequestBody(codigoProducto);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/CONSULTAGRAL/Stock")
                .header("Authorization", "Bearer " + token)
                .post(requestBody)
                .build();



        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {

                requireActivity().runOnUiThread(() -> {
                    progressBarStock.setVisibility(View.GONE);
                    mostrarError("Error de conexión: " + e.getMessage());
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (!response.isSuccessful()) {

                    requireActivity().runOnUiThread(() -> {
                        progressBarStock.setVisibility(View.GONE);
                        mostrarError("Error en el servidor: " + response.code());
                    });
                    return;
                }

                procesarRespuesta(response.body().string());
            }
        });
    }

    private void procesarRespuesta(String responseData) {
        try {
            Log.d("StockFragment", "Respuesta recibida: " + responseData);
            JSONObject jsonResponse = new JSONObject(responseData);
            JSONObject cuerpo = jsonResponse.getJSONObject("Cuerpo");
            JSONArray bienesArray = cuerpo.getJSONArray("BIENES");
            List<ItemStock> items = new ArrayList<>();

            Log.d("StockFragment", "Número de bienes: " + bienesArray.length());

            for (int i = 0; i < bienesArray.length(); i++) {
                JSONObject bien = bienesArray.getJSONObject(i);
                String codigo = bien.getString("Codigo");
                String descripcion = bien.getString("Descripcion");
                // En este caso, el talle viene en cada stock
                String tallaBase = "UNICA"; // Valor por defecto

                if (bien.has("Stocks")) {
                    JSONArray stocksArray = bien.getJSONArray("Stocks");

                    for (int j = 0; j < stocksArray.length(); j++) {
                        JSONObject stockObj = stocksArray.getJSONObject(j);
                        if (stockObj.getString("Empresa").equals(codEmpresa)) {
                            String talla = stockObj.optString("Talle", tallaBase);
                            double stock = stockObj.getDouble("StockDisponible");
                            String sucursal = stockObj.getString("Deposito");
                            String nombreSucursal = stockObj.getString("DescripcionDeposito");

                            items.add(new ItemStock(sucursal, nombreSucursal, talla, stock));
                        }
                    }
                } else {
                    // Si no hay stocks, agregamos un ítem con valores por defecto
                    items.add(new ItemStock("N/A", "N/A", tallaBase, 0));
                }
            }

            requireActivity().runOnUiThread(() -> {
                progressBarStock.setVisibility(View.GONE);
                actualizarLista(items);
            });

        } catch (JSONException e) {
            Log.e("StockFragment", "Error parsing JSON", e);
            requireActivity().runOnUiThread(() -> {
                progressBarStock.setVisibility(View.GONE);
                Toast.makeText(getContext(), "Error al procesar los datos", Toast.LENGTH_LONG).show();
            });
        }
    }

    private void actualizarLista(List<ItemStock> items) {
        requireActivity().runOnUiThread(() -> {
            Log.d("StockFragment", "Actualizando lista con " + items.size() + " items");

            if (items.isEmpty()) {
                Toast.makeText(getContext(), "No se encontraron resultados", Toast.LENGTH_SHORT).show();
                recyclerViewStock.setVisibility(View.GONE);
            } else {
                recyclerViewStock.setVisibility(View.VISIBLE);
                adapter.setItems(items);
                Log.d("StockFragment", "Adapter item count: " + adapter.getItemCount());

                // Forzar redibujado
                recyclerViewStock.invalidate();
                recyclerViewStock.requestLayout();
            }
        });
    }

    private void mostrarError(String mensaje) {
        requireActivity().runOnUiThread(() ->
                Toast.makeText(getContext(), mensaje, Toast.LENGTH_LONG).show()
        );
    }

    private RequestBody crearRequestBody(String codigoProducto) {
        try {
            JSONObject body = new JSONObject();
            JSONObject header = new JSONObject();
            header.put("etiqueta", "CONSULTAGRAL");
            header.put("codemp", codEmpresa);
            header.put("codsuc", codSucursal);
            header.put("fecha", fechaFormateada);

            JSONArray filtros = new JSONArray();
            JSONObject filtro = new JSONObject();
            filtro.put("TagEntidad", "Bien");
            filtro.put("NombreCampo", "Codigo");
            filtro.put("Comparacion", "0");
            filtro.put("Valor", codigoProducto);
            filtros.put(filtro);

            body.put("header", header);
            body.put("consultaGral", new JSONObject().put("FiltrosAdicionales", filtros));

            return RequestBody.create(body.toString(), MediaType.parse("application/json"));
        } catch (JSONException e) {
            return null;
        }
    }

    private static class StockAdapter extends RecyclerView.Adapter<StockAdapter.ViewHolder> {
        private List<ItemStock> items = new ArrayList<>();

        void setItems(List<ItemStock> newItems) {
            this.items = new ArrayList<>(newItems); // Crear nueva lista
            notifyDataSetChanged();
            Log.d("StockAdapter", "Items actualizados: " + items.size());
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            // Asegúrate que este ID coincida con tu archivo item_stock.xml
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_stock, parent, false);
            return new ViewHolder(view);

        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            ItemStock item = items.get(position);

            // Forzar visualización de datos
            holder.tvSucursal.setText(item.sucursal != null ? item.sucursal : "N/A");
            holder.tvNombreSucursal.setText(item.NombreSucursal != null ? item.NombreSucursal : "");
            holder.tvTalla.setText(item.talla != null ? item.talla : "");
            holder.tvStock.setText(String.valueOf(item.stock));

            // Debug: Marcar items alternos
            holder.itemView.setBackgroundColor(position % 2 == 0 ?
                    Color.parseColor("#FFFFFF") : Color.parseColor("#F5F5F5"));
        }

        @Override
        public int getItemCount() {
            return items.size();
        }

        static class ViewHolder extends RecyclerView.ViewHolder {
            final TextView tvSucursal, tvNombreSucursal, tvTalla, tvStock;

            ViewHolder(View itemView) {
                super(itemView);
                tvSucursal = itemView.findViewById(R.id.tvSucursal);
                tvNombreSucursal = itemView.findViewById(R.id.tvNombreSucursal);
                tvTalla = itemView.findViewById(R.id.tvTalla);
                tvStock = itemView.findViewById(R.id.tvStock);
            }
        }
    }

    private static class ItemStock {
        final String sucursal, NombreSucursal, talla;
        final double stock;

        ItemStock(String sucursal, String NombreSucursal, String talla, double stock) {
            this.sucursal = sucursal;
            this.NombreSucursal = NombreSucursal;
            this.talla = talla;
            this.stock = stock;
        }
    }
}
