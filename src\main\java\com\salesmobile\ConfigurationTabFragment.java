package com.salesmobile;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.salesmobile.config.SharedPreferences;

public class ConfigurationTabFragment extends Fragment {

    private TextView tvConfigServidor, tvConfigBaseDatos, tvConfigPuntoVenta;
    private TextView tvConfigNombrePuntoVenta, tvConfigPrefijoPuntoVenta;
    private TextView tvConfigCodigoVendedor;
    private TextView tvConfigCotizacion;
    private TextView tvConfigCodEmpresa, tvConfigCodSucursal;
    private TextView tvConfigUrlApi, tvConfigUsuarioApi, tvConfigClaveApi;
    private TextView tvConfigUrlApi2, tvConfigUsuarioApi2, tvConfigClaveApi2;
    private TextView tvConfigCodEmpresa2, tvConfigCodSucursal2;
    private SharedPreferences sharedPreferences;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.tab_configuration, container, false);

        sharedPreferences = new SharedPreferences(getContext());

        initializeViews(view);
        loadConfigurationData();

        return view;
    }

    private void initializeViews(View view) {
        // Configuración de Conexión
        tvConfigServidor = view.findViewById(R.id.tvConfigServidor);
        tvConfigBaseDatos = view.findViewById(R.id.tvConfigBaseDatos);
        tvConfigPuntoVenta = view.findViewById(R.id.tvConfigPuntoVenta);

        // Información del Punto de Venta
        tvConfigNombrePuntoVenta = view.findViewById(R.id.tvConfigNombrePuntoVenta);
        tvConfigPrefijoPuntoVenta = view.findViewById(R.id.tvConfigPrefijoPuntoVenta);

        // Información del Vendedor
        tvConfigCodigoVendedor = view.findViewById(R.id.tvConfigCodigoVendedor);

        // Cotización
        tvConfigCotizacion = view.findViewById(R.id.tvConfigCotizacion);

        // Configuración de APIs
        tvConfigCodEmpresa = view.findViewById(R.id.tvConfigCodEmpresa);
        tvConfigCodSucursal = view.findViewById(R.id.tvConfigCodSucursal);
        tvConfigUrlApi = view.findViewById(R.id.tvConfigUrlApi);
        tvConfigUsuarioApi = view.findViewById(R.id.tvConfigUsuarioApi);
        tvConfigClaveApi = view.findViewById(R.id.tvConfigClaveApi);

        // API Secundaria
        tvConfigUrlApi2 = view.findViewById(R.id.tvConfigUrlApi2);
        tvConfigUsuarioApi2 = view.findViewById(R.id.tvConfigUsuarioApi2);
        tvConfigClaveApi2 = view.findViewById(R.id.tvConfigClaveApi2);
        tvConfigCodEmpresa2 = view.findViewById(R.id.tvConfigCodEmpresa2);
        tvConfigCodSucursal2 = view.findViewById(R.id.tvConfigCodSucursal2);
    }

    private void loadConfigurationData() {
        // Configuración de Conexión
        tvConfigServidor.setText(getValueOrDefault(sharedPreferences.getServidor()));
        tvConfigBaseDatos.setText(getValueOrDefault(sharedPreferences.getBaseDatos()));
        tvConfigPuntoVenta.setText(getValueOrDefault(sharedPreferences.getPuntoVenta()));

        // Información del Punto de Venta
        SharedPreferences.PuntoVentaInfo puntoVentaInfo = sharedPreferences.getPuntoVentaInfo();
        tvConfigNombrePuntoVenta.setText(getValueOrDefault(puntoVentaInfo.getNombre()));
        tvConfigPrefijoPuntoVenta.setText(getValueOrDefault(puntoVentaInfo.getPrefijo()));

        // Información del Vendedor
        SharedPreferences.VendedorInfo vendedorInfo = sharedPreferences.getVendedorInfo();
        tvConfigCodigoVendedor.setText(getValueOrDefault(vendedorInfo.getCodigo()));

        // Cotización
        float cotizacion = sharedPreferences.getCotizacion();
        tvConfigCotizacion.setText(String.valueOf(cotizacion));

        // Configuración de APIs
        java.util.List<SharedPreferences.ConfiguracionSincronizada> configuraciones = sharedPreferences.getSyncedConfiguraciones();
        if (!configuraciones.isEmpty()) {
            SharedPreferences.ConfiguracionSincronizada config = configuraciones.get(0);

            // API Principal
            tvConfigCodEmpresa.setText(getValueOrDefault(config.getCodempresa()));
            tvConfigCodSucursal.setText(getValueOrDefault(config.getCodsucursal()));
            tvConfigUrlApi.setText(getValueOrDefault(config.getUrlapi()));
            tvConfigUsuarioApi.setText(getValueOrDefault(config.getUsuario()));
            tvConfigClaveApi.setText(getValueOrDefault(config.getClave()));

            // API Secundaria
            tvConfigUrlApi2.setText(getValueOrDefault(config.getUrlapi2()));
            tvConfigUsuarioApi2.setText(getValueOrDefault(config.getUsuario2()));
            tvConfigClaveApi2.setText(getValueOrDefault(config.getClave2()));
            tvConfigCodEmpresa2.setText(getValueOrDefault(config.getCodempresa2()));
            tvConfigCodSucursal2.setText(getValueOrDefault(config.getCodsucursal2()));
        } else {
            // No hay configuraciones sincronizadas
            tvConfigCodEmpresa.setText("No configurado");
            tvConfigCodSucursal.setText("No configurado");
            tvConfigUrlApi.setText("No configurado");
            tvConfigUsuarioApi.setText("No configurado");
            tvConfigClaveApi.setText("No configurado");
            tvConfigUrlApi2.setText("No configurado");
            tvConfigUsuarioApi2.setText("No configurado");
            tvConfigClaveApi2.setText("No configurado");
            tvConfigCodEmpresa2.setText("No configurado");
            tvConfigCodSucursal2.setText("No configurado");
        }
    }

    private String getValueOrDefault(String value) {
        return (value != null && !value.trim().isEmpty()) ? value : "No configurado";
    }
}
