package com.salesmobile;
import android.content.Context;
import com.salesmobile.config.ParametrosConf;
import android.content.SharedPreferences;
import android.icu.text.SimpleDateFormat;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class IndicesApiClient {
    private static final String TAG = "IndicesApiClient";
    private static final String SHARED_PREFS_NAME = "ConfigDB";
    private static final String VALOR1_KEY = "valor1_indices";


    private final Context context;
    private final OkHttpClient client;
    private final Gson gson;
    private final Executor executor;
    private final AppDatabase db;

    public IndicesApiClient(Context context) {
        this.context = context.getApplicationContext();
        this.client = new OkHttpClient();
        this.gson = new GsonBuilder()
                .registerTypeAdapter(Date.class, new DateDeserializer())
                .create();
        this.executor = Executors.newSingleThreadExecutor();
        this.db = AppDatabase.getInstance(context);
    }

    public void fetchIndicesAndSaveValor1(String authToken, IndicesCallback callback) {
        executor.execute(() -> {
            try {

                String apiUrl = ParametrosConf.API2_URL + "/api/v2/Indices";
                Log.d(TAG, "Consultando endpoint: " + apiUrl);

                // 1. Crear y ejecutar la solicitud HTTP
                Request request = new Request.Builder()
                        .url(apiUrl)
                        .addHeader("accept", "text/plain")
                        .addHeader("Authorization", "Bearer " + authToken)
                        .build();

                Response response = client.newCall(request).execute();

                // 2. Procesar respuesta
                if (!response.isSuccessful()) {
                    String errorMsg = "Error en la respuesta: " + response.code();
                    Log.e(TAG, errorMsg);
                    callback.onError(errorMsg);
                    return;
                }

                if (response.body() == null) {
                    String errorMsg = "Cuerpo de respuesta vacío";
                    Log.e(TAG, errorMsg);
                    callback.onError(errorMsg);
                    return;
                }

                String responseBody = response.body().string();
                processResponse(responseBody, callback);

            } catch (IOException e) {
                String errorMsg = "Error de red: " + e.getMessage();
                Log.e(TAG, errorMsg, e);
                callback.onError(errorMsg);
            } catch (Exception e) {
                String errorMsg = "Error inesperado: " + e.getMessage();
                Log.e(TAG, errorMsg, e);
                callback.onError(errorMsg);
            }
        });
    }

    private void processResponse(String jsonResponse, IndicesCallback callback) {
        try {
            Type listType = new TypeToken<List<Indice>>() {}.getType();
            List<Indice> indices = gson.fromJson(jsonResponse, listType);

            if (indices == null || indices.isEmpty()) {
                callback.onError("No se encontraron datos en la respuesta");
                return;
            }

            // Buscar el registro con Tabla = 1 y fecha más reciente
            Indice latestIndice = null;

            for (Indice indice : indices) {
                if (indice != null && indice.getTabla() == 1) {
                    if (latestIndice == null ||
                            (indice.getFecha() != null && latestIndice.getFecha() != null &&
                                    indice.getFecha().after(latestIndice.getFecha()))) {
                        latestIndice = indice;
                    }
                }
            }

            if (latestIndice == null) {
                callback.onError("No se encontraron datos para Tabla = 1");
                return;
            }

            if (latestIndice.getValor1() <= 0) {
                callback.onError("Valor1 no válido: " + latestIndice.getValor1());
                return;
            }

            // Guardar el valor y notificar éxito
            saveValor1ToSharedPreferences(latestIndice.getValor1());
            callback.onSuccess(latestIndice.getValor1());

        } catch (JsonSyntaxException e) {
            String errorMsg = "Error al parsear JSON: " + e.getMessage();
            Log.e(TAG, errorMsg, e);
            callback.onError(errorMsg);
        } catch (Exception e) {
            String errorMsg = "Error al procesar respuesta: " + e.getMessage();
            Log.e(TAG, errorMsg, e);
            callback.onError(errorMsg);
        }
    }

    private void saveValor1ToSharedPreferences(double valor1) {
        try {
            SharedPreferences sharedPreferences = context.getSharedPreferences(SHARED_PREFS_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putFloat(VALOR1_KEY, (float) valor1);
            editor.apply();
            Log.d(TAG, "Valor1 guardado correctamente: " + valor1);
        } catch (Exception e) {
            Log.e(TAG, "Error al guardar en SharedPreferences", e);
        }
    }

    public static double getSavedValor1(Context context) {
        try {
            SharedPreferences sharedPreferences = context.getSharedPreferences(SHARED_PREFS_NAME, Context.MODE_PRIVATE);
            return sharedPreferences.getFloat(VALOR1_KEY, 0f);
        } catch (Exception e) {
            Log.e(TAG, "Error al leer de SharedPreferences", e);
            return 0;
        }
    }

    public interface IndicesCallback {
        void onSuccess(double valor1);
        void onError(String message);
    }

    // Clase modelo para los datos del índice con anotaciones Gson si es necesario
    public static class Indice {
        private int Tabla;
        private Date Fecha;
        private double Valor1;
        private double Valor2;
        private double Valor3;

        // Getters
        public int getTabla() { return Tabla; }
        public Date getFecha() { return Fecha; }
        public double getValor1() { return Valor1; }
        public double getValor2() { return Valor2; }
        public double getValor3() { return Valor3; }
    }

    // Deserializador personalizado para fechas si es necesario
    private static class DateDeserializer implements JsonDeserializer<Date> {
        @Override
        public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
                throws JsonParseException {
            try {
                String dateStr = json.getAsString();
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
                return format.parse(dateStr);
            } catch (Exception e) {
                Log.e(TAG, "Error al parsear fecha", e);
                return null;
            }
        }
    }
}