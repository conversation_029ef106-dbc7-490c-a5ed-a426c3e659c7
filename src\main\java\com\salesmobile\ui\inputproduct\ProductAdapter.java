package com.salesmobile.ui.inputproduct;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.TextView;


import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.salesmobile.R;

import java.util.List;

public class ProductAdapter extends RecyclerView.Adapter<ProductAdapter.ProductViewHolder> {
    private List<Product> productList;
    private OnDeleteClickListener deleteClickListener;

    public interface OnDeleteClickListener {
        void onDeleteClick(int position);
    }

    public ProductAdapter(List<Product> productList, OnDeleteClickListener listener) {
        this.productList = productList;
        this.deleteClickListener = listener;
    }

    @NonNull
    @Override
    public ProductViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_product, parent, false);
        return new ProductViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ProductViewHolder holder, int position) {
        Product product = productList.get(position);

        holder.tvName.setText(product.getName());
        holder.tvCode.setText(product.getCode());
        holder.tvPrice.setText(String.format("$%.2f", product.getPrice()));
        holder.tvSize.setText("Talla: " + product.getSize());
        holder.tvQuantity.setText("Cant: " + product.getQuantity());

        holder.btnDelete.setOnClickListener(v -> {
            if (deleteClickListener != null) {
                deleteClickListener.onDeleteClick(position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return productList.size();
    }

    public static class ProductViewHolder extends RecyclerView.ViewHolder {
        TextView tvName, tvCode, tvPrice, tvSize, tvQuantity;
        ImageButton btnDelete;

        public ProductViewHolder(@NonNull View itemView) {
            super(itemView);
            tvName = itemView.findViewById(R.id.productName);
            tvCode = itemView.findViewById(R.id.tvProductCode);
            tvPrice = itemView.findViewById(R.id.productPrice);
            tvSize = itemView.findViewById(R.id.productSize);
            tvQuantity = itemView.findViewById(R.id.productQuantity);
            btnDelete = itemView.findViewById(R.id.btnRemove);
        }
    }
}