package com.salesmobile.ui.client;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
public class DialogUtils {
    public interface OnDialogClickListener {
        void onPositiveClick();
        void onNegativeClick();
    }

    public static void showYesNoDialog(Context context, String title, String message, final OnDialogClickListener listener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle(title)
                .setMessage(message)
                .setPositiveButton("Sí", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        if (listener != null) {
                            listener.onPositiveClick();
                        }
                    }
                })
                .setNegativeButton("No", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        if (listener != null) {
                            listener.onNegativeClick();
                        }
                    }
                })
                .show();
    }
}
