<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.SalesMobile" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:textColor">@color/black</item>

        <!-- Customize your theme here. -->
    </style>

    <style name="Theme.SalesMobile.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Theme.SalesMobile.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.SalesMobile.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="SpinnerSelectedItem">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@android:color/black</item>
    </style>

    <style name="ScannerDialog" parent="Theme.MaterialComponents.DayNight.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowMinWidthMajor">0%</item>
        <item name="android:windowMinWidthMinor">0%</item>
    </style>


</resources>