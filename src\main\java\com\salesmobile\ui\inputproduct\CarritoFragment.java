package com.salesmobile.ui.inputproduct;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.GridView;
import android.widget.ListView;
import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.widget.TextView;
import android.widget.Toast;

import com.salesmobile.R;
import com.salesmobile.ui.articulos.ViewArticulos;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;


public class CarritoFragment extends Fragment {
    private RecyclerView recyclerView;
    private CarritoAdapter adapter;
    private TextView totalAmount;
    private Button btnCobrar, btnCloseCart;
    private SharedViewModel viewModel;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_carrito, container, false);

        // Inicializar vistas
        recyclerView = view.findViewById(R.id.gridViewProducts);
        totalAmount = view.findViewById(R.id.totalAmount);
        btnCobrar = view.findViewById(R.id.btnCobrar);
        btnCloseCart = view.findViewById(R.id.btnCloseCart);

        // Configurar RecyclerView
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        adapter = new CarritoAdapter(new ArrayList<>(), position -> {
            // Manejar eliminación de producto
            viewModel.removeProductFromCart(position);
            updateTotal();
        });
        recyclerView.setAdapter(adapter);

        // Observar cambios en el ViewModel
        viewModel = new ViewModelProvider(requireActivity()).get(SharedViewModel.class);
        viewModel.getCartProducts().observe(getViewLifecycleOwner(), productos -> {
            adapter.updateList(productos);
            updateTotal();
        });

        // Configurar botones
        btnCobrar.setOnClickListener(v -> realizarCobro());
        btnCloseCart.setOnClickListener(v -> volverAProductos());

        return view;
    }

    private void updateTotal() {
        double total = 0;
        for (Product producto : viewModel.getCartProducts().getValue()) {
            total += producto.getPrice() * producto.getQuantity();
        }
        NumberFormat format = NumberFormat.getInstance(new Locale("es", "PY"));
        format.setMaximumFractionDigits(0); // sin decimales

        String formatted = "Gs " + format.format(total);
        totalAmount.setText(formatted);
        //totalAmount.setText(String.format("Gs %.2f", total));
    }

    private void realizarCobro() {
        // Implementar lógica de cobro
        List<Product> productos = viewModel.getCartProducts().getValue();
        if (productos == null || productos.isEmpty()) {
            Toast.makeText(getContext(), "El carrito está vacío", Toast.LENGTH_SHORT).show();
            return;
        }

        procesarVenta(productos);
    }

    private void procesarVenta(List<Product> productos) {
        // Convertir productos a JSON
        JSONArray productosArray = new JSONArray();
        for (Product producto : productos) {
            JSONObject item = new JSONObject();
            try {
                item.put("codigo", producto.getCode());
                item.put("talla", producto.getSize());
                item.put("precio", producto.getPrice());
                item.put("cantidad", producto.getQuantity());
                productosArray.put(item);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        NavController navController = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
        navController.navigate(R.id.action_nav_carritoFragment_to_fragment_select_client);

    }

    private void volverAProductos() {
        NavController navController = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
        navController.navigate(R.id.nav_inputProduct);
    }
}