package com.salesmobile;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.salesmobile.config.ParametrosConf;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class SucursalInfoManager {
    private static final String TAG = "SucursalInfoManager";
    private static final String SHARED_PREFS_NAME = "SucursalInfo";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    
    // Keys para SharedPreferences
    private static final String KEY_NOMBRE = "sucursal_nombre";
    private static final String KEY_DOMICILIO = "sucursal_domicilio";
    private static final String KEY_DOMICILIO2 = "sucursal_domicilio2";
    private static final String KEY_LOCALIDAD = "sucursal_localidad";
    private static final String KEY_TELEFONO = "sucursal_telefono";
    private static final String KEY_CODSUC = "sucursal_codsuc";
    
    private final Context context;
    private final OkHttpClient client;
    
    public interface SucursalInfoCallback {
        void onSuccess(SucursalInfo sucursalInfo);
        void onError(String errorMessage);
    }
    
    public static class SucursalInfo {
        private String nombre;
        private String domicilio;
        private String domicilio2;
        private String localidad;
        private String telefono;
        private int codsuc;
        
        // Constructor
        public SucursalInfo(String nombre, String domicilio, String domicilio2, 
                           String localidad, String telefono, int codsuc) {
            this.nombre = nombre != null ? nombre.trim() : "";
            this.domicilio = domicilio != null ? domicilio.trim() : "";
            this.domicilio2 = domicilio2 != null ? domicilio2.trim() : "";
            this.localidad = localidad != null ? localidad.trim() : "";
            this.telefono = telefono != null ? telefono.trim() : "";
            this.codsuc = codsuc;
        }
        
        // Getters
        public String getNombre() { return nombre; }
        public String getDomicilio() { return domicilio; }
        public String getDomicilio2() { return domicilio2; }
        public String getLocalidad() { return localidad; }
        public String getTelefono() { return telefono; }
        public int getCodsuc() { return codsuc; }
    }
    
    public SucursalInfoManager(Context context) {
        this.context = context;
        this.client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .followRedirects(true)
                .followSslRedirects(true)
                .build();
    }
    
    public void consultarInfoSucursal(String codemp, String codsuc, SucursalInfoCallback callback) {
        new Thread(() -> {
            try {

                JSONObject requestJson = new JSONObject();
                requestJson.put("codemp", codemp);
                requestJson.put("codsuc", codsuc);
                Log.d(TAG, "JSON de petición: " + requestJson.toString());
                if("true".equals(ParametrosConf.TEST_MODE_VALUE)) {
                    requestJson.put("TEST", ParametrosConf.TEST_MODE_VALUE);
                    requestJson.put("TESTPC", ParametrosConf.TEST_PC_VALUE);
                    Log.d(TAG, "JSON de petición con TEST: " + requestJson.toString());
                }
     
                RequestBody body = RequestBody.create(requestJson.toString(), JSON);
                com.salesmobile.config.ParametrosConf.refresh();
                String url = ParametrosConf.BASE_URL + "consultarInfoSucursal.php";
                Log.d(TAG, "URL de consulta: " + url);
                
                Request request = new Request.Builder()
                        .url(url)
                        .header("Content-Type", "application/json")
                        .header("Accept", "application/json")
                        .post(body)
                        .build();
                
                Log.d(TAG, "Enviando petición: " + requestJson.toString());
                
                try (Response response = client.newCall(request).execute()) {
                    if (response.body() == null) {
                        callback.onError("Respuesta vacía del servidor");
                        return;
                    }
                    
                    String responseBody = response.body().string();
                    Log.d(TAG, "Respuesta recibida: " + responseBody);
                    Log.d(TAG, "Código de respuesta: " + response.code());
                    
                    if (response.isSuccessful()) {
                        procesarRespuesta(responseBody, callback);
                    } else {
                        callback.onError("Error en la petición: " + response.code() + " - " + response.message());
                    }
                }
                
            } catch (IOException e) {
                Log.e(TAG, "Error de conexión", e);
                String errorMsg = "Error de conexión: ";
                if (e instanceof java.net.SocketTimeoutException) {
                    errorMsg += "Tiempo de espera agotado. Verifique su conexión a internet.";
                } else if (e instanceof java.net.UnknownHostException) {
                    errorMsg += "No se puede resolver el servidor. Verifique su conexión a internet.";
                } else if (e instanceof java.net.ConnectException) {
                    errorMsg += "No se puede conectar al servidor.";
                } else {
                    errorMsg += e.getMessage();
                }
                callback.onError(errorMsg);
            } catch (JSONException e) {
                Log.e(TAG, "Error al crear JSON", e);
                callback.onError("Error al crear la petición: " + e.getMessage());
            }
        }).start();
    }

    private String obtenerUrlDesdeConfiguracion() {
        try {
            com.salesmobile.config.SharedPreferences sharedPrefs = new com.salesmobile.config.SharedPreferences(context);
            java.util.List<com.salesmobile.config.SharedPreferences.ConfiguracionSincronizada> configuraciones = sharedPrefs.getSyncedConfiguraciones();

            if (!configuraciones.isEmpty()) {
                String baseUrl = ParametrosConf.BASE_URL;
                if (baseUrl != null && !baseUrl.trim().isEmpty()) {
                    // Asegurar que la URL termine con /
                    if (!baseUrl.endsWith("/")) {
                        baseUrl += "/";
                    }
                    Log.d(TAG, "URL obtenida desde ParametrosConf.BASE_URL: " + baseUrl);
                    return baseUrl;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al obtener URL desde configuración: " + e.getMessage());
        }

        // Fallback a la URL por defecto si no se puede obtener desde configuración
        Log.d(TAG, "Usando URL por defecto: " + ParametrosConf.BASE_URL);
        return ParametrosConf.BASE_URL;
    }

    private void procesarRespuesta(String responseBody, SucursalInfoCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);
            String status = jsonResponse.getString("status");
            
            if ("success".equals(status)) {
                JSONArray dataArray = jsonResponse.getJSONArray("data");
                if (dataArray.length() > 0) {
                    JSONObject sucursalData = dataArray.getJSONObject(0);
                    
                    SucursalInfo sucursalInfo = new SucursalInfo(
                            sucursalData.getString("NOMBRE"),
                            sucursalData.getString("DOMICILIO"),
                            sucursalData.getString("DOMICILIO2"),
                            sucursalData.getString("LOCALIDAD"),
                            sucursalData.getString("TELEFONO"),
                            sucursalData.getInt("CODSUC")
                    );
                    
                    guardarSucursalInfo(sucursalInfo);
                    
                    callback.onSuccess(sucursalInfo);
                } else {
                    callback.onError("No se encontraron datos de la sucursal");
                }
            } else {
                callback.onError("Error en la respuesta del servidor");
            }
            
        } catch (JSONException e) {
            Log.e(TAG, "Error al procesar respuesta JSON", e);
            callback.onError("Error al procesar la respuesta: " + e.getMessage());
        }
    }
    
    private void guardarSucursalInfo(SucursalInfo sucursalInfo) {
        SharedPreferences prefs = context.getSharedPreferences(SHARED_PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        editor.putString(KEY_NOMBRE, sucursalInfo.getNombre());
        editor.putString(KEY_DOMICILIO, sucursalInfo.getDomicilio());
        editor.putString(KEY_DOMICILIO2, sucursalInfo.getDomicilio2());
        editor.putString(KEY_LOCALIDAD, sucursalInfo.getLocalidad());
        editor.putString(KEY_TELEFONO, sucursalInfo.getTelefono());
        editor.putInt(KEY_CODSUC, sucursalInfo.getCodsuc());
        
        editor.apply();
        
        Log.d(TAG, "Información de sucursal guardada en SharedPreferences");
    }
    
    public static SucursalInfo getSucursalInfoGuardada(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(SHARED_PREFS_NAME, Context.MODE_PRIVATE);
        
        String nombre = prefs.getString(KEY_NOMBRE, "");
        String domicilio = prefs.getString(KEY_DOMICILIO, "");
        String domicilio2 = prefs.getString(KEY_DOMICILIO2, "");
        String localidad = prefs.getString(KEY_LOCALIDAD, "");
        String telefono = prefs.getString(KEY_TELEFONO, "");
        int codsuc = prefs.getInt(KEY_CODSUC, 0);

        if (nombre.isEmpty() && domicilio.isEmpty() && codsuc == 0) {
            return null;
        }
        
        return new SucursalInfo(nombre, domicilio, domicilio2, localidad, telefono, codsuc);
    }
    
    public static void limpiarSucursalInfo(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(SHARED_PREFS_NAME, Context.MODE_PRIVATE);
        prefs.edit().clear().apply();
    }
}
