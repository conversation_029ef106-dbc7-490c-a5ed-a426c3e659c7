package com.salesmobile;

import android.content.Context;
import android.content.SharedPreferences;

import java.util.concurrent.TimeUnit;

public class TokenManager2 {

    private static TokenManager2 instance;
    private String token;
    private long tokenExpirationTime;
    private boolean isTokenRequestInProgress = false;


    private TokenManager2() {
        // Constructor privado para evitar instanciación directa
    }

    public static synchronized TokenManager2 getInstance() {
        if (instance == null) {
            instance = new TokenManager2();
        }
        return instance;
    }

    public void requestToken(Context context, Runnable onTokenReceived) {
        String existingToken = getToken(context);
        if (existingToken != null) {
            token = existingToken;
            if (onTokenReceived != null) {
                onTokenReceived.run();
            }
            return;
        }

        if (isTokenRequestInProgress) {
            return;
        }

        isTokenRequestInProgress = true;

        TokenRequest2 tokenRequest2 = new TokenRequest2(context, new TokenRequest2.TokenRequestListener() {
            @Override
            public void onTokenReceived(String receivedToken) {
                token = receivedToken;
                tokenExpirationTime = System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(30);
                isTokenRequestInProgress = false;

                saveToken(context, token, tokenExpirationTime);

                if (onTokenReceived != null) {
                    onTokenReceived.run();
                }
            }
        });
        tokenRequest2.execute();
    }

    private void saveToken(Context context, String token, long expirationTime) {
        SharedPreferences sharedPreferences = context.getSharedPreferences("AppPrefs", Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString("token2", token);
        editor.putLong("tokenExpirationTime2", expirationTime);
        editor.apply();
    }

    private String getToken(Context context) {
        SharedPreferences sharedPreferences = context.getSharedPreferences("AppPrefs", Context.MODE_PRIVATE);
        long expirationTime = sharedPreferences.getLong("tokenExpirationTime2", 0);

        if (System.currentTimeMillis() < expirationTime) {
            return sharedPreferences.getString("token2", null);
        } else {
            return null; // El token ha expirado
        }
    }

    public String getToken() {
        return token;
    }
}