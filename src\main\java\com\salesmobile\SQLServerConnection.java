package com.salesmobile;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.AsyncTask;
import android.util.Log;
import android.widget.EditText;
import android.widget.Toast;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class SQLServerConnection {
    public interface ConnectionCallback {
        void onComplete(Connection connection, boolean syncSuccess, String syncMessage);
    }

    public static void getConnection(Context context, String servidor, String basedatos,
                                     String usuario, String password,
                                     SharedPreferences sharedPreferences,
                                     ConnectionCallback callback) {
        new AsyncTask<Void, Void, Connection>() {
            @Override
            protected Connection doInBackground(Void... voids) {
                String url = "jdbc:jtds:sqlserver://" + servidor +":1433/" + basedatos +
                        ";user=" + usuario + ";password=" + password + ";loginTimeout=5;";
                try {
                    Class.forName("net.sourceforge.jtds.jdbc.Driver");
                    return DriverManager.getConnection(url);
                } catch (Exception e) {
                    Log.e("SQLServerConnection", "Error de conexión: " + e.getMessage());
                    return null;
                }
            }

            @Override
            protected void onPostExecute(Connection connection) {
                if (connection != null) {
                    new DatabaseSynchronizer(context, sharedPreferences)
                            .startSyncWithExistingConnection(connection, new DatabaseSynchronizer.SyncListener() {
                                @Override
                                public void onSyncComplete(boolean success, String message) {
                                    Log.d("Sincronización", message);
                                    callback.onComplete(connection, success, message);
                                }
                            });
                } else {
                    callback.onComplete(null, false, "Error de conexión a SQL Server");
                }
            }
        }.execute();
    }
}