package com.salesmobile.ui.inputproduct;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.List;

public class SharedViewModel extends ViewModel {
    private final MutableLiveData<List<Product>> productListLiveData = new MutableLiveData<>(new ArrayList<>());
    private final MutableLiveData<JSONObject> facturaDataLiveData = new MutableLiveData<>();
    private final MutableLiveData<JSONObject> reciboDataLiveData = new MutableLiveData<>();
    private final MutableLiveData<JSONObject> pdfDataLiveData = new MutableLiveData<>();

    public LiveData<List<Product>> getProductListLiveData() {
        return productListLiveData;
    }

    public LiveData<List<Product>> getCartProducts() {
        return productListLiveData;
    }

    public void addProductToCart(Product product) {
        List<Product> currentList = productListLiveData.getValue();
        if (currentList != null) {
            currentList.add(product);
            productListLiveData.setValue(currentList);
        }
    }

    public void limpiarCarrito() {
        productListLiveData.setValue(new ArrayList<>());
    }

    public void removeProductFromCart(int position) {
        List<Product> currentProducts = productListLiveData.getValue();
        if (currentProducts != null && position >= 0 && position < currentProducts.size()) {
            currentProducts.remove(position);
            productListLiveData.setValue(currentProducts);
        }
    }

    // Métodos para manejar datos de factura
    public LiveData<JSONObject> getFacturaDataLiveData() {
        return facturaDataLiveData;
    }

    public void setFacturaData(JSONObject facturaData) {
        facturaDataLiveData.setValue(facturaData);
    }

    public void clearFacturaData() {
        facturaDataLiveData.setValue(null);
    }

    // Métodos para manejar datos de recibo
    public LiveData<JSONObject> getReciboDataLiveData() {
        return reciboDataLiveData;
    }

    public void setReciboData(JSONObject reciboData) {
        reciboDataLiveData.setValue(reciboData);
    }

    public void clearReciboData() {
        reciboDataLiveData.setValue(null);
    }

    // Métodos para manejar datos de PDF
    public LiveData<JSONObject> getPdfDataLiveData() {
        return pdfDataLiveData;
    }

    public void setPdfData(JSONObject pdfData) {
        pdfDataLiveData.setValue(pdfData);
    }

    public void clearPdfData() {
        pdfDataLiveData.setValue(null);
    }

}