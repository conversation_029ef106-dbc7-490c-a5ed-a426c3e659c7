<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="Escanea este código para pagar"
        android:textSize="18sp"
        android:layout_marginBottom="16dp"/>

    <ImageView
        android:id="@+id/qrImageView"
        android:layout_width="300dp"
        android:layout_height="300dp"
        android:layout_gravity="center"/>

    <TextView
        android:id="@+id/textHookAlias"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Hook Alias"
        android:textStyle="bold"
        android:layout_marginTop="12dp"
        android:textAlignment="center"
        android:textColor="#000000"
        android:textSize="18sp"
        android:visibility="gone"/>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="8dp"/>

    <!-- Spinner y texto para procesamiento de pago -->
    <LinearLayout
        android:id="@+id/layoutProcesandoPago"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="16dp"
        android:visibility="gone">

        <ProgressBar
            android:id="@+id/progressBarProcesando"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"/>

        <TextView
            android:id="@+id/textProcesandoPago"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="Procesando Pago..."
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#4CAF50"
            android:layout_marginTop="8dp"/>

    </LinearLayout>

    <Button
        android:id="@+id/btnClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="Cancelar"
        android:layout_marginTop="16dp"/>

</LinearLayout>