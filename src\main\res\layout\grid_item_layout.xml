<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp">

    <!-- Vista para el código de producto -->
    <TextView
        android:id="@+id/tvProductCode"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/black"
        android:textSize="16sp"/>

    <!-- Vista para la Tamaño -->
    <TextView
        android:id="@+id/tvSize"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="16sp"/>


    <!-- Vista para la cantidad seleccionada -->
    <TextView
        android:id="@+id/tvCantidadSeleccionada"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="16sp"/>

    <!-- Vista para precio -->
    <TextView
        android:id="@+id/tvPrecio"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="16sp"/>

    <Button
        android:id="@+id/btnEliminar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Borrar" />

</LinearLayout>

