package com.salesmobile.ui.factura;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

@Entity(tableName = "ventas")
public class Venta {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    private Long id;

    @ColumnInfo(name = "fecha")
    private String fecha;

    @ColumnInfo(name = "total")
    @NonNull
    private Double total;

    @ColumnInfo(name = "numTransacciones")
    @NonNull
    private Integer numTransacciones;

    // Constructor principal para Room (con todos los campos)
    public Venta(String fecha, @NonNull Double total, @NonNull Integer numTransacciones) {
        this.fecha = fecha;
        this.total = total;
        this.numTransacciones = numTransacciones;
    }


    @Ignore
    public Venta(String fecha, double total) {
        this(fecha, total, 1);
    }

    // Getters y Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFecha() {
        return fecha;
    }

    public void setFecha(String fecha) {
        this.fecha = fecha;
    }

    @NonNull
    public Double getTotal() {
        return total;
    }

    public void setTotal(@NonNull Double total) {
        this.total = total;
    }

    @NonNull
    public Integer getNumTransacciones() {
        return numTransacciones;
    }

    public void setNumTransacciones(@NonNull Integer numTransacciones) {
        this.numTransacciones = numTransacciones;
    }
}