package com.salesmobile;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

public class DatabaseHelper extends SQLiteOpenHelper {

    private static final String DATABASE_NAME = "sqlite_database";
    private static final int DATABASE_VERSION = 5;

    // Tabla configuraciones
    public static final String TABLE_CONFIGURACIONES = "configuraciones";
    public static final String CONFIG_ID = "id";
    public static final String CONFIG_URLAPI = "baseUrl";
    public static final String CONFIG_USUARIO = "usuario";
    public static final String CONFIG_CLAVE = "contrasena";
    public static final String CONFIG_GRANTTYPE = "grantType";
    public static final String CONFIG_CODEMPRESA = "codigoEmpresa";
    public static final String CONFIG_CODSUCURSAL = "codigoSucursal";
    public static final String CONFIG_URLAPI2 = "baseUrl2";
    public static final String CONFIG_USUARIO2 = "usuario2";
    public static final String CONFIG_CLAVE2 = "contrasena2";
    public static final String CONFIG_GRANTTYPE2 = "grantType2";
    public static final String CONFIG_CODEMPRESA2 = "codigoEmpresa2";
    public static final String CONFIG_CODSUCURSAL2 = "codigoSucursal2";
    public static final String CONFIG_ID_PUNTOVENTA = "id_puntoventa";

    // Tabla puntosdeventas
    public static final String TABLE_PUNTOS_VENTA = "puntosdeventas";
    public static final String PV_ID = "id";
    public static final String PV_NOMBRE = "nombre";
    public static final String PV_PREFIJO = "prefijo";

    // Tabla vendedores
    public static final String TABLE_VENDEDORES = "vendedores";
    public static final String VEND_CODIGO = "codigo";
    public static final String VEND_NOMBRE = "nombre";
    public static final String VEND_USUARIOSQL = "usuariosql";
    public static final String VEND_SOLOCONSULTA = "soloconsulta";

    public static final String TABLE_VENTAS = "ventas";
    public static final String VENTA_ID = "id";
    public static final String VENTA_FECHA = "fecha";
    public static final String VENTA_TOTAL = "total";
    public static final String VENTA_NUM_TRANSACCIONES = "numTransacciones";

    // Tabla Parametros
    public static final String TABLE_PARAMETROS = "parametros";
    public static final String TEST = "test";
    public static final String TESTPC = "testpc";
    public static final String ENTORNO = "entorno";
    public static final String DEV_BANCARD_URL = "devBancardUrl";
    public static final String DEV_BANCARD_PUBLIC_KEY = "devBancardPublicKey";
    public static final String DEV_BANCARD_PRIVATE_KEY = "devBancardPrivateKey";
    public static final String PROD_BANCARD_URL = "prodBancardUrl";
    public static final String PROD_BANCARD_PUBLIC_KEY = "prodBancardPublicKey";
    public static final String PROD_BANCARD_PRIVATE_KEY = "prodBancardPrivateKey";
    public static final String DEV_DEFAULT_URL = "devDefaultUrl";
    public static final String PROD_DEFAULT_URL = "prodDefaultUrl";
    public static final String BANCARD_COMMERCE_CODE = "bancardCommerceCode";
    public static final String BANCARD_COMMERCE_BRANCH = "bancardCommerceBranch";
    public static final String APPMOVIL_DEFAULT_URL = "appmovilDefaultUrl";
    public static final String VER_CONFIG = "ver_config";
    public static final String ID_PARAMETROS = "id_parametros";


    // Sentencias SQL para crear tablas
    private static final String CREATE_TABLE_CONFIGURACIONES =
            "CREATE TABLE " + TABLE_CONFIGURACIONES + " (" +
                    CONFIG_ID + " INTEGER NOT NULL PRIMARY KEY, " +
                    CONFIG_URLAPI + " TEXT, " +
                    CONFIG_USUARIO + " TEXT, " +
                    CONFIG_CLAVE + " TEXT, " +
                    CONFIG_GRANTTYPE + " TEXT, " +
                    CONFIG_CODEMPRESA + " TEXT, " +
                    CONFIG_CODSUCURSAL + " TEXT, " +
                    CONFIG_URLAPI2 + " TEXT, " +
                    CONFIG_USUARIO2 + " TEXT, " +
                    CONFIG_CLAVE2 + " TEXT, " +
                    CONFIG_GRANTTYPE2 + " TEXT, " +
                    CONFIG_CODEMPRESA2 + " TEXT, " +
                    CONFIG_CODSUCURSAL2 + " TEXT, " +
                    CONFIG_ID_PUNTOVENTA + " INTEGER NOT NULL)";

    private static final String CREATE_TABLE_PUNTOS_VENTA =
            "CREATE TABLE " + TABLE_PUNTOS_VENTA + " (" +
                    PV_NOMBRE + " TEXT, " +
                    PV_PREFIJO + " TEXT, " +
                    PV_ID + " INTEGER NOT NULL PRIMARY KEY)";

    private static final String CREATE_TABLE_VENDEDORES =
            "CREATE TABLE " + TABLE_VENDEDORES + " (" +
                    VEND_CODIGO + " TEXT PRIMARY KEY, " +
                    VEND_NOMBRE + " TEXT, " +
                    VEND_USUARIOSQL + " TEXT, " +
                    VEND_SOLOCONSULTA + " TEXT)";

    private static final String CREATE_TABLE_VENTAS =
            "CREATE TABLE " + TABLE_VENTAS + " (" +
                    VENTA_ID + " INTEGER PRIMARY KEY, " +
                    VENTA_FECHA + " TEXT, " +
                    VENTA_TOTAL + " REAL NOT NULL, " +
                    VENTA_NUM_TRANSACCIONES + " INTEGER NOT NULL)";

    private static final String CREATE_TABLE_PARAMETROS =
            "CREATE TABLE " + TABLE_PARAMETROS + " (" +
                    TEST + " TEXT, " +
                    TESTPC + " TEXT, " +
                    ENTORNO + " TEXT, " +
                    DEV_BANCARD_URL + " TEXT, " +
                    DEV_BANCARD_PUBLIC_KEY + " TEXT, " +
                    DEV_BANCARD_PRIVATE_KEY + " TEXT, " +
                    PROD_BANCARD_URL + " TEXT, " +
                    PROD_BANCARD_PUBLIC_KEY + " TEXT, " +
                    PROD_BANCARD_PRIVATE_KEY + " TEXT, " +
                    DEV_DEFAULT_URL + " TEXT, " +
                    PROD_DEFAULT_URL + " TEXT, " +
                    BANCARD_COMMERCE_CODE + " INT, " +
                    BANCARD_COMMERCE_BRANCH + " INT, " +
                    APPMOVIL_DEFAULT_URL + " TEXT, " +
                    VER_CONFIG + " INT, " +
                    ID_PARAMETROS + " INTEGER NOT NULL PRIMARY KEY)";


    public DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        // Crear todas las tablas
        db.execSQL(CREATE_TABLE_CONFIGURACIONES);
        db.execSQL(CREATE_TABLE_PUNTOS_VENTA);
        db.execSQL(CREATE_TABLE_VENDEDORES);
        db.execSQL(CREATE_TABLE_VENTAS);
        db.execSQL(CREATE_TABLE_PARAMETROS);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        // Eliminar tablas antiguas si existen
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_CONFIGURACIONES);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_PUNTOS_VENTA);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_VENDEDORES);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_VENTAS);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_PARAMETROS);
        // Volver a crear las tablas
        onCreate(db);
    }
}