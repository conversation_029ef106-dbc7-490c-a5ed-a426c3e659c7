package com.salesmobile;

import android.content.Context;
import android.os.AsyncTask;
import android.util.Log;

import com.salesmobile.config.ParametrosConf;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;

import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class TokenRequest extends AsyncTask<Void, Void, String> {

    private Context context; // Contexto para acceder a la base de datos
    private TokenRequestListener listener; // Listener para manejar el token

    // Constructor para establecer el listener y el contexto
    public TokenRequest(Context context, TokenRequestListener listener) {
        this.context = context;
        this.listener = listener;
    }


    @Override
    protected String doInBackground(Void... voids) {
        // Obtener la configuración desde la base de datos
        AppDatabase db = AppDatabase.getInstance(context);
        Configuracion configuracion = db.configuracionDao().obtenerConfiguracion();

        if (configuracion == null) {
            Log.e("TokenRequest", "No se encontró la configuración en la base de datos");
            return null;
        }

        String username = configuracion.getUsuario();
        String password = configuracion.getContrasena();
        String grantType = configuracion.getGrantType();

        OkHttpClient client = new OkHttpClient();

        // Crear el cuerpo de la solicitud con los valores de la configuración
        RequestBody formBody = new FormBody.Builder()
                .add("grant_type", grantType)
                .add("client_id", "api")
                .add("client_secret", "secret")
                .add("refresh_token", "")
                .add("username", username)
                .add("password", password)
                .build();

        // Crear la solicitud HTTP
        Request request = new Request.Builder()
                .url(ParametrosConf.API1_URL + "/auth/token")
                .header("accept", "text/plain")
                .post(formBody)
                .build();

        // Log de la solicitud
        Log.d("API Request", request.toString());

        // Ejecutar la solicitud y obtener la respuesta
        try (Response response = client.newCall(request).execute()) {
            return response.body().string();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    protected void onPostExecute(String jsonResult) {
        if (jsonResult != null) {
            try {
                // Parsear el JSON para obtener el token
                JSONObject jsonObject = new JSONObject(jsonResult);
                String accessToken = jsonObject.getString("access_token");

                // Llamar al listener con el token
                if (listener != null) {
                    listener.onTokenReceived(accessToken);
                }
            } catch (JSONException e) {
                e.printStackTrace();
                if (listener != null) {
                    listener.onTokenReceived(null);
                }
            }
        } else {
            // Si no se obtuvo respuesta, llamar al listener con null
            if (listener != null) {
                listener.onTokenReceived(null);
            }
        }
    }

    // Interfaz de devolución de llamada para manejar el token
    public interface TokenRequestListener {
        void onTokenReceived(String token);
    }
}