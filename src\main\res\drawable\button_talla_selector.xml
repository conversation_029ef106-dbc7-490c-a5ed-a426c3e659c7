<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/selectedColor"/>
            <corners android:radius="4dp"/>
            <stroke android:width="1dp" android:color="@color/selectedBorder"/>
        </shape>
    </item>
    <item android:state_enabled="true" android:state_selected="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/availableColor"/>
            <corners android:radius="4dp"/>
            <stroke android:width="1dp" android:color="@color/buttonBorder"/>
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/unavailableColor"/>
            <corners android:radius="4dp"/>
            <stroke android:width="1dp" android:color="@color/buttonBorder"/>
        </shape>
    </item>
</selector>