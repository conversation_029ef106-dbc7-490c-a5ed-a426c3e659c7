package com.salesmobile.ui.client;

import androidx.annotation.NonNull;

import java.io.EOFException;
import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;

public class RetryInterceptor implements Interceptor {
    private final int maxRetries;

    public RetryInterceptor(int maxRetries) {
        this.maxRetries = maxRetries;
    }

    @NonNull
    @Override
    public okhttp3.Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        okhttp3.Response response = null;
        IOException exception = null;

        // Intentar hasta maxRetries veces
        for (int i = 0; i < maxRetries; i++) {
            try {
                response = chain.proceed(request);
                if (response.isSuccessful()) {
                    return response;
                }
            } catch (IOException e) {
                exception = e;
                if (e instanceof EOFException) {
                    // Esperar antes de reintentar (backoff exponencial)
                    try {
                        Thread.sleep(2000L * (i + 1));
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("Interrupted during retry", ie);
                    }
                    continue;
                }
                break;
            }
        }

        throw exception != null ? exception : new IOException("Falló después de " + maxRetries + " intentos");
    }
}