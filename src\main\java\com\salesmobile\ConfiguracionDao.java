package com.salesmobile;



import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;
import com.salesmobile.Configuracion;

@Dao
public interface ConfiguracionDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE) // Reemplaza el registro si ya existe
    void insert(Configuracion configuracion);

    @Update
    void update(Configuracion configuracion);

    @Query("SELECT * FROM configuraciones LIMIT 1")
    Configuracion obtenerConfiguracion();

    @Query("DELETE FROM configuraciones")
    void eliminarConfiguraciones();
}