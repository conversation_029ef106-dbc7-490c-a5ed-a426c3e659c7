package com.salesmobile;

import android.util.Base64;
import android.util.Log;

import java.io.IOException;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class ApiClient {
    private static final String TAG = "ApiClient";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    private static ApiClient instance;
    private final OkHttpClient client;
    private final Executor executor;

    private ApiClient() {
        this.client = new OkHttpClient.Builder()
                .connectTimeout(15, TimeUnit.SECONDS)
                .readTimeout(15, TimeUnit.SECONDS)
                .writeTimeout(15, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
        this.executor = Executors.newCachedThreadPool();
    }

    public static synchronized ApiClient getInstance() {
        if (instance == null) {
            instance = new ApiClient();
        }
        return instance;
    }

    public interface ApiCallback {
        void onSuccess(String response);
        void onError(String errorMessage);
    }

    public void postWithBasicAuth(String url, String requestBody, String username, String password, ApiCallback callback) {
        executor.execute(() -> {
            try {
                String credentials = username + ":" + password;
                String basicAuth = "Basic " + Base64.encodeToString(credentials.getBytes(), Base64.NO_WRAP);

                RequestBody body = requestBody != null ?
                        RequestBody.create(requestBody, JSON) :
                        RequestBody.create("", JSON);

                Request request = new Request.Builder()
                        .url(url)
                        .addHeader("Authorization", basicAuth)
                        .post(body)
                        .build();

                Log.d(TAG, "Realizando POST con Basic Auth: " + url);

                try (Response response = client.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        String errorMsg = "Error HTTP: " + response.code() + " - " + response.message();
                        Log.e(TAG, errorMsg);
                        callback.onError(errorMsg);
                        return;
                    }

                    if (response.body() == null) {
                        callback.onError("Respuesta vacía");
                        return;
                    }

                    String responseBody = response.body().string();
                    Log.d(TAG, "POST exitoso");
                    callback.onSuccess(responseBody);
                }

            } catch (IOException e) {
                String errorMsg = "Error de red: " + e.getMessage();
                Log.e(TAG, errorMsg, e);
                callback.onError(errorMsg);
            } catch (Exception e) {
                String errorMsg = "Error: " + e.getMessage();
                Log.e(TAG, errorMsg, e);
                callback.onError(errorMsg);
            }
        });
    }
}