<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#40000000"
    android:padding="32dp">

<androidx.cardview.widget.CardView
    android:layout_width="match_parent"
    android:layout_height="500dp"
    android:layout_gravity="center"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp"
    android:background="@color/black">

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <!-- Vista previa de la cámara -->
    <androidx.camera.view.PreviewView
        android:id="@+id/previewView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Botón cerrar en la esquina superior derecha -->
    <ImageButton
        android:id="@+id/buttonCloseScanner"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="16dp"
        android:src="@drawable/ic_close"
        android:background="@drawable/circle_background"
        android:contentDescription="Cerrar escáner"
        android:scaleType="centerInside"
        android:padding="12dp"
        android:tint="@android:color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>

</FrameLayout>
