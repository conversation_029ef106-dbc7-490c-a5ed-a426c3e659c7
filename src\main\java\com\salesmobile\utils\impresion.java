package com.salesmobile.utils;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.util.Log;

import androidx.core.content.FileProvider;

import java.io.File;

public class impresion {

    public interface PrintCallback {
        void onSuccess();
        void onError(String errorMessage);
    }

    public static void imprimirPDFDesdeArchivo(Context context, String pdfPath, String tipoDocumento, PrintCallback callback) {
        File pdfFile = new File(pdfPath);
        if (!pdfFile.exists()) {
            Log.d("PRINT_ERROR", "Archivo de " + tipoDocumento.toLowerCase() + " no encontrado");
            if (callback != null) {
                callback.onError("Archivo de " + tipoDocumento.toLowerCase() + " no encontrado");
            }
            return;
        }

        try {
            Uri fileUri = FileProvider.getUriForFile(context,
                    context.getPackageName() + ".provider",
                    pdfFile);

            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("application/pdf");
            shareIntent.putExtra(Intent.EXTRA_STREAM, fileUri);
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            shareIntent.putExtra(Intent.EXTRA_SUBJECT, tipoDocumento);
            shareIntent.putExtra(Intent.EXTRA_TEXT, "Adjunto " + tipoDocumento.toLowerCase() + " en formato PDF.");

            context.startActivity(Intent.createChooser(shareIntent, "Compartir " + tipoDocumento.toLowerCase() + " mediante"));
            Log.d("PRINT", tipoDocumento + " compartido exitosamente");

            if (callback != null) {
                callback.onSuccess();
            }

        } catch (Exception e) {
            Log.e("PRINT_ERROR", "Error al compartir " + tipoDocumento.toLowerCase() + ": " + e.getMessage());
            if (callback != null) {
                callback.onError("Error al compartir " + tipoDocumento.toLowerCase() + ": " + e.getMessage());
            }
        }
    }
}
