<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.SalesMobile" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color adaptado para modo noche -->
        <item name="colorPrimary">@color/purple_200</item>                 <!-- Más suave -->
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>

        <!-- Secondary brand color adaptado -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>

        <!-- Status bar -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>

        <!-- Texto general blanco sobre fondo oscuro -->
        <item name="android:textColor">@color/white</item>
    </style>

    <!-- Tema sin ActionBar (igual que en modo día) -->
    <style name="Theme.SalesMobile.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Overlays (igual que en modo día) -->
    <style name="Theme.SalesMobile.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />
    <style name="Theme.SalesMobile.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <!-- Spinner adaptado para modo noche -->
    <style name="SpinnerSelectedItem">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@android:color/white</item>
    </style>
</resources>