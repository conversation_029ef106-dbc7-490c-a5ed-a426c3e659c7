<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:id="@+id/productImage"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:src="@drawable/ic_product_placeholder"
            android:contentDescription="Imagen del producto"/>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/productName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"/>

            <TextView
                android:id="@+id/productSize"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"/>

            <TextView
                android:id="@+id/productPrice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/purple_500"
                android:textSize="14sp"/>

            <TextView
                android:id="@+id/productQuantity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/grey"
                android:textSize="14sp"/>
        </LinearLayout>

        <ImageButton
            android:id="@+id/btnRemove"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_delete"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Eliminar producto"/>
    </LinearLayout>
</androidx.cardview.widget.CardView>