<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="navigation_view">

    <group android:checkableBehavior="single">
        <item
            android:id="@+id/nav_home"
            android:icon="@drawable/ic_home"
            android:title="@string/menu_home" />
        <item
            android:id="@+id/nav_clientFragment"
            android:icon="@drawable/ic_clients"
            android:title="@string/menu_client" />
        <item
            android:id="@+id/nav_inputProduct"
            android:icon="@drawable/ic_add_product"
            android:title="@string/menu_inputProduct" />
        <item
            android:id="@+id/nav_stock"
            android:icon="@drawable/ic_stock"
            android:title="Stock" />
        <item
            android:id="@+id/nav_configuracion"
            android:icon="@drawable/ic_settings"
            android:title="Configuracion" />
        <item
            android:id="@+id/nav_close"
            android:icon="@drawable/ic_logout"
            android:title="Salir del Sistema" />
    </group>
</menu>