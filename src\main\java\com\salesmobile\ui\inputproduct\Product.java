package com.salesmobile.ui.inputproduct;
public class Product {
    private String code;
    private String name;
    private double price;
    private String size;
    private int quantity;

    // Constructor
    public Product(String code, String name, double price, String size, int quantity) {
        this.code = code;
        this.name = name;
        this.price = price;
        this.size = size;
        this.quantity = quantity;
    }

    // Getters
    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public double getPrice() {
        return price;
    }

    public String getSize() {
        return size;
    }

    public int getQuantity() {
        return quantity;
    }
}