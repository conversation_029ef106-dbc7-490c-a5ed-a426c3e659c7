025-07-30 15:04:43.502 26267-30937 Volley                  com.salesmobile                      D  [36854] NetworkUtility.logSlowRequests: HTTP response for request=<[ ] http://**************:5050/API/COMPROBANTEVENTA 0x31e503b5 NORMAL 1> [lifetime=3134], [size=238], [rc=200], [retryCount=1]
2025-07-30 15:04:43.510 26267-26267 FacturaResponse         com.salesmobile                      D  Respuesta exitosa: {"codigoEstado":400,"codigoEstadoBAS":1012,"cuerpo":"El número de cupón manual ya fué utilizado.(ControlNroCupon)(SP_ICR_COMPROB_VENTA_PAGO_TARJETA)","informacionExtra":null,"pdf":null,"pdFerror":null,"informacionProximaConsulta":null}
2025-07-30 15:04:43.514 26267-26267 AUDIT                   com.salesmobile                      D  Enviando auditoría transacción: {"codemp":"1","codsuc":"33","TEST":"true","TESTPC":"**************","proc":"SP_INSERT_AUDIT_TRANSAC","params":{"@CODCMP":"FB","@PREFIJO":"001-001","@FECHA":"2025-07-30 16:04:43","@OPERADOR":"Z999","@TOTAL":"22170.00","@TIMESTAMP":"2025-07-30 16:04:43","@HOSTNAME":"MOV1-SUC33","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"El número de cupón manual ya fué utilizado.(ControlNroCupon)(SP_ICR_COMPROB_VENTA_PAGO_TARJETA)\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SWYLO85092"}}
2025-07-30 15:04:43.520 26267-30954 DB                      com.salesmobile                      D  Actualizado correctamente. ID: 1
2025-07-30 15:04:43.523 26267-30954 DB                      com.salesmobile                      D  Total ventas en BD: 1
2025-07-30 15:04:43.535 26267-26267 ScrollerOp...ionManager com.salesmobile                      D  registerConfigChangedListener
2025-07-30 15:04:43.543 26267-26267 ScrollerOp...ionManager com.salesmobile                      D  registerConfigChangedListener
2025-07-30 15:04:43.560 26267-30957 Volley                  com.salesmobile                      E  [36862] NetworkUtility.shouldRetryException: Unexpected response code 500 for http://**************/appmovil/api_testing/webservice.php
2025-07-30 15:04:43.575 26267-29829 libc                    com.salesmobile                      W  Access denied finding property "ro.vendor.display.iris_x7.support"
2025-07-30 15:04:43.576 26267-26267 ForceDarkHelperStubImpl com.salesmobile                      I  setViewRootImplForceDark: false for com.salesmobile.MainActivity@734302, reason: AppDarkModeEnable
2025-07-30 15:04:43.576 26267-26267 VRI[MainActivity]       com.salesmobile                      D  hardware acceleration = true, forceHwAccelerated = false
2025-07-30 15:04:43.636 26267-26267 BufferQueueConsumer     com.salesmobile                      D  [](id:669b00000004,api:0,p:-1,c:26267) connect: controlledByApp=false
2025-07-30 15:04:43.637 26267-26267 ForceDarkHelperStubImpl com.salesmobile                      I  setViewRootImplForceDark: false for com.salesmobile.MainActivity@734302, reason: AppDarkModeEnable
2025-07-30 15:04:43.639 26267-29829 OpenGLRenderer          com.salesmobile                      E  Unable to match the desired swap behavior.
2025-07-30 15:04:43.641 26267-26267 VRI[MainActivity]       com.salesmobile                      D  vri.reportNextDraw android.view.ViewRootImpl.performTraversals:4376 android.view.ViewRootImpl.doTraversal:2992 android.view.ViewRootImpl$TraversalRunnable.run:10469 android.view.Choreographer$CallbackRecord.run:1671 android.view.Choreographer$CallbackRecord.run:1680 
2025-07-30 15:04:43.642 26267-26267 VRI[MainActivity]       com.salesmobile                      D  vri.Setup new sync=wmsSync-VRI[MainActivity]#10
2025-07-30 15:04:43.649 26267-29829 OpenGLRenderer          com.salesmobile                      D  makeCurrent grContext:0xb400007d8e73e300 reset mTextureAvailable
2025-07-30 15:04:43.655 26267-29829 BLASTBufferQueue        com.salesmobile                      D  [VRI[MainActivity]#4](f:0,a:1) acquireNextBufferLocked size=1202x727 mFrameNumber=1 applyTransaction=true mTimestamp=611284534466358(auto) mPendingTransactions.size=0 graphicBufferId=112815905964051 transform=0
2025-07-30 15:04:43.655 26267-26267 VRI[MainActivity]       com.salesmobile                      D  vri.reportDrawFinished
2025-07-30 15:04:43.657 26267-26267 AUDIT                   com.salesmobile                      E  Error en auditoría transacción: com.android.volley.ServerError
2025-07-30 15:04:43.659 26267-26267 AUDIT                   com.salesmobile                      D  Enviando auditoría item: {"codemp":"1","codsuc":"33","proc":"SP_INSERT_AUDIT_TRANSAC_ITEMS","TEST":"true","TESTPC":"**************","params":{"@CODITM":"201840,0","@CANTIDAD1":"1","@PRECIO":"22170.0","@IMPORTE":"22170.00"}}
2025-07-30 15:04:43.781 26267-30968 Volley                  com.salesmobile                      E  [36867] NetworkUtility.shouldRetryException: Unexpected response code 500 for http://**************/appmovil/api_testing/webservice.php
2025-07-30 15:04:43.781 26267-26267 AUDIT                   com.salesmobile                      E  Error en auditoría item: com.android.volley.ServerError
2025-07-30 15:07:24.658 26267-26267 WindowOnBackDispatcher  com.salesmobile                      W  sendCancelIfRunning: isInProgress=falsecallback=android.app.Dialog$$ExternalSyntheticLambda2@17e782a
2025-07-30 15:07:24.663 26267-29829 OpenGLRenderer          com.salesmobile                      D  endAllActiveAnimators on 0xb400007dfaa30c00 (RippleDrawable) with handle 0xb400007d6afd11e0
2025-07-30 15:07:24.664 26267-26267 View                    com.salesmobile                      D  [Warning] assignParent to null: this = DecorView@1ccdd7e[MainActivity]
2025-07-30 15:07:24.666 26267-29895 BLASTBufferQueue        com.salesmobile                      D  [VRI[MainActivity]#4](f:0,a:1) destructor()
2025-07-30 15:07:24.666 26267-29895 BufferQueueConsumer     com.salesmobile                      D  [VRI[MainActivity]#4(BLAST Consumer)4](id:669b00000004,api:0,p:-1,c:26267) disconnect


USE [SUC33]
GO
/****** Object:  StoredProcedure [dbo].[SP_INSERT_AUDIT_TRANSAC_ITEMS]    Script Date: 30/07/2025 15:22:03 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER    PROCEDURE [dbo].[SP_INSERT_AUDIT_TRANSAC_ITEMS]
    @IDCOMPROBANTE [int],
    @CODITM [VARCHAR](18),
    @CANTIDAD1 [dbo].[cantidad],
    @PRECIO [dbo].[importe],
    @IMPORTE [dbo].[importe]
AS
BEGIN
    SET NOCOUNT ON;
    
    INSERT INTO [dbo].[AUDIT_TRANSAC_ITEMS] (
        [IDCOMPROBANTE],
        [CODITM],
        [CANTIDAD1],
        [PRECIO],
        [IMPORTE]
    ) VALUES (
        @IDCOMPROBANTE,
        @CODITM,
        @CANTIDAD1,
        @PRECIO,
        @IMPORTE
    );
    
    RETURN 0;
END;


USE [SUC33]
GO

/****** Object:  StoredProcedure [dbo].[SP_INSERT_AUDIT_TRANSAC]    Script Date: 30/07/2025 16:09:30 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



ALTER   PROCEDURE [dbo].[SP_INSERT_AUDIT_TRANSAC]
    @IDCOMPROBANTE [int],
    @CODCMP [VARCHAR](2),
    @PREFIJO [dbo].[PREFIJO],
    @FECHA [datetime],
    @OPERADOR [varchar](16),
    @TOTAL [dbo].[importe],
    @TIMESTAMP [datetime],
    @HOSTNAME [varchar](16),
    @ACCION [VARCHAR](1),
    @LOGAPP [varchar](max),
    @HOOKALIAS [varchar](12)
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @TIMESTAMP IS NULL
        SET @TIMESTAMP = GETDATE();
    
    INSERT INTO [dbo].[AUDIT_TRANSAC] (
        [IDCOMPROBANTE],
        [CODCMP],
        [PREFIJO],
        [FECHA],
        [OPERADOR],
        [TOTAL],
        [TIMESTAMP],
        [HOSTNAME],
        [ACCION],
        [LOGAPP],
        [HOOKALIAS]
    ) VALUES (
        @IDCOMPROBANTE,
        @CODCMP,
        @PREFIJO,
        @FECHA,
        @OPERADOR,
        @TOTAL,
        @TIMESTAMP,
        @HOSTNAME,
        @ACCION,
        @LOGAPP,
        @HOOKALIAS
    );
    
    RETURN 0;
END;
GO


backend logs

[2025-07-30 20:04:51] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","TEST":"true","TESTPC":"**************","proc":"SP_INSERT_AUDIT_TRANSAC","params":{"@CODCMP":"FB","@PREFIJO":"001-001","@FECHA":"2025-07-30 15:04:50","@OPERADOR":"Z999","@TOTAL":"44340.00","@TIMESTAMP":"2025-07-30 15:04:50","@HOSTNAME":"MOV1-SUC33","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"El n\u00famero de cup\u00f3n manual ya fu\u00e9 utilizado.(ControlNroCupon)(SP_ICR_COMPROB_VENTA_PAGO_TARJETA)\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SQWRN47196"}}}
[2025-07-30 20:04:51] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":false,"test":"true","testPC":"**************"}
[2025-07-30 20:04:51] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC","params":{"@CODCMP":"FB","@PREFIJO":"001-001","@FECHA":"2025-07-30 15:04:50","@OPERADOR":"Z999","@TOTAL":"44340.00","@TIMESTAMP":"2025-07-30 15:04:50","@HOSTNAME":"MOV1-SUC33","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"El n\u00famero de cup\u00f3n manual ya fu\u00e9 utilizado.(ControlNroCupon)(SP_ICR_COMPROB_VENTA_PAGO_TARJETA)\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SQWRN47196"},"codemp":"1","codsuc":"33","test":"true","testPC":"**************"}
[2025-07-30 20:04:51] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"**************","procedure":"SP_INSERT_AUDIT_TRANSAC"}
[2025-07-30 20:04:51] Error crítico en webservice - Data: {"error":"Fallo la consulta: SQLSTATE: 42000, Code: 8114, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Error converting data type varchar to int.","trace":"#0 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(116): DatabaseQueryExecutor->executeProcedure('SP_INSERT_AUDIT...', Array, '1', '33', 'true', '**************')\n#1 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(273): ejecutarWebservice(Array)\n#2 {main}"}
[2025-07-30 20:04:51] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","proc":"SP_INSERT_AUDIT_TRANSAC_ITEMS","TEST":"true","TESTPC":"**************","params":{"@CODITM":"201840,0","@CANTIDAD1":"2","@PRECIO":"22170.0","@IMPORTE":"44340.00"}}}
[2025-07-30 20:04:51] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":false,"test":"true","testPC":"**************"}
[2025-07-30 20:04:51] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC_ITEMS","params":{"@CODITM":"201840,0","@CANTIDAD1":"2","@PRECIO":"22170.0","@IMPORTE":"44340.00"},"codemp":"1","codsuc":"33","test":"true","testPC":"**************"}
[2025-07-30 20:04:51] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"**************","procedure":"SP_INSERT_AUDIT_TRANSAC_ITEMS"}
[2025-07-30 20:04:51] Error crítico en webservice - Data: {"error":"Fallo la consulta: SQLSTATE: 42000, Code: 8114, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Error converting data type varchar to int.","trace":"#0 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(116): DatabaseQueryExecutor->executeProcedure('SP_INSERT_AUDIT...', Array, '1', '33', 'true', '**************')\n#1 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(273): ejecutarWebservice(Array)\n#2 {main}"}
[2025-07-30 21:04:44] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","TEST":"true","TESTPC":"**************","proc":"SP_INSERT_AUDIT_TRANSAC","params":{"@CODCMP":"FB","@PREFIJO":"001-001","@FECHA":"2025-07-30 16:04:43","@OPERADOR":"Z999","@TOTAL":"22170.00","@TIMESTAMP":"2025-07-30 16:04:43","@HOSTNAME":"MOV1-SUC33","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"El n\u00famero de cup\u00f3n manual ya fu\u00e9 utilizado.(ControlNroCupon)(SP_ICR_COMPROB_VENTA_PAGO_TARJETA)\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SWYLO85092"}}}
[2025-07-30 21:04:44] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":false,"test":"true","testPC":"**************"}
[2025-07-30 21:04:44] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC","params":{"@CODCMP":"FB","@PREFIJO":"001-001","@FECHA":"2025-07-30 16:04:43","@OPERADOR":"Z999","@TOTAL":"22170.00","@TIMESTAMP":"2025-07-30 16:04:43","@HOSTNAME":"MOV1-SUC33","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"El n\u00famero de cup\u00f3n manual ya fu\u00e9 utilizado.(ControlNroCupon)(SP_ICR_COMPROB_VENTA_PAGO_TARJETA)\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SWYLO85092"},"codemp":"1","codsuc":"33","test":"true","testPC":"**************"}
[2025-07-30 21:04:44] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"**************","procedure":"SP_INSERT_AUDIT_TRANSAC"}
[2025-07-30 21:04:44] Error crítico en webservice - Data: {"error":"Fallo la consulta: SQLSTATE: 42000, Code: 8114, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Error converting data type varchar to int.","trace":"#0 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(116): DatabaseQueryExecutor->executeProcedure('SP_INSERT_AUDIT...', Array, '1', '33', 'true', '**************')\n#1 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(273): ejecutarWebservice(Array)\n#2 {main}"}
[2025-07-30 21:04:44] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","proc":"SP_INSERT_AUDIT_TRANSAC_ITEMS","TEST":"true","TESTPC":"**************","params":{"@CODITM":"201840,0","@CANTIDAD1":"1","@PRECIO":"22170.0","@IMPORTE":"22170.00"}}}
[2025-07-30 21:04:44] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":false,"test":"true","testPC":"**************"}
[2025-07-30 21:04:44] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC_ITEMS","params":{"@CODITM":"201840,0","@CANTIDAD1":"1","@PRECIO":"22170.0","@IMPORTE":"22170.00"},"codemp":"1","codsuc":"33","test":"true","testPC":"**************"}
[2025-07-30 21:04:44] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"**************","procedure":"SP_INSERT_AUDIT_TRANSAC_ITEMS"}
[2025-07-30 21:04:44] Error crítico en webservice - Data: {"error":"Fallo la consulta: SQLSTATE: 42000, Code: 8114, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Error converting data type varchar to int.","trace":"#0 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(116): DatabaseQueryExecutor->executeProcedure('SP_INSERT_AUDIT...', Array, '1', '33', 'true', '**************')\n#1 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(273): ejecutarWebservice(Array)\n#2 {main}"}