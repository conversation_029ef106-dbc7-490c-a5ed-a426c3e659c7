025-07-30 15:01:28.782 25591-25591 WindowOnBackDispatcher  com.salesmobile                      W  sendCancelIfRunning: isInProgress=falsecallback=android.app.Dialog$$ExternalSyntheticLambda2@9a36891
2025-07-30 15:01:28.799 25591-25591 View                    com.salesmobile                      D  [Warning] assignParent to null: this = DecorView@b31bd54[MainActivity]
2025-07-30 15:01:28.872 25591-25591 FacturaRequest          com.salesmobile                      D  JSON a enviar: {"HEADER":{"ETIQUETA":"COMPROBANTES DE VENTA","CODEMP":"1","CODSUC":"33","FECHA":"30\/07\/2025"},"ComprobanteVenta":{"TipoABC":"B","PuntoVenta":"MOV1-SUC33","Fecha":"30\/07\/2025","Comprobante":"FB","Prefijo":"001-001","Cliente_Codigo":"C3984771","Vendedor_Codigo":"Z999","Cobrador_Codigo":"Z999","Operador":"cajerom","Cajero":"cajerom","CondicionImpositiva":"F","CodigoCondicionVenta":"V01","TotalGravado":"80618.20","TotalIVA":"8061.80","totalNoInscripto":"0","totalImpuestoInterno":"0","TotalPercepcionIVA":"0","totalPercepcionIngresosBrutos":"0","Total":"88680.00","Observaciones":"Venta desde app móvil - Hookalias: SWIYH06891 Fecha: 30\/07\/2025 16:01:28","Items":[{"CodigoItem":"201840,5","NumeroUnidadMedida":"1","CantidadPrimeraUnidad":"2","PrecioUnitario":"22170.0","ImporteGravado":"40309.10","ImporteIva":"4030.90","ImportePercepcionIva":"0","ImporteTotal":"44340.00","TasaIva":"10"},{"CodigoItem":"201840,6","NumeroUnidadMedida":"1","CantidadPrimeraUnidad":"2","PrecioUnitario":"22170.0","ImporteGravado":"40309.10","ImporteIva":"4030.90","ImportePercepcionIva":"0","ImporteTotal":"44340.00","TasaIva":"10"}],"tarjetas":[{"MedioPago":"03","codigoTarjeta":"00","Fecha":"30\/07\/2025","numeroTarjeta":"2045","numeroCupon":"74156","codigoTerminal":"01","tipoCupon":"C","telefono":"","nombre":"","plan":"BAN","Importe":"88680.00"}]}}
2025-07-30 15:01:28.878 25591-25591 Volley                  com.salesmobile                      D  [2] DiskBasedCache.clear: Cache cleared.
2025-07-30 15:01:30.011 25591-25591 FacturaResponse         com.salesmobile                      D  Respuesta exitosa: {"codigoEstado":200,"codigoEstadoBAS":0,"cuerpo":"El comprobante de venta se importó correctamente con IdComprobante:57677","informacionExtra":{"codcmp":"FB","prefijo":"001-001","numero":"49112"},"pdf":null,"pdFerror":null,"informacionProximaConsulta":null}
2025-07-30 15:01:30.021 25591-25591 Compatibil...geReporter com.salesmobile                      D  Compat change id reported: 147798919; UID 10809; state: ENABLED
2025-07-30 15:01:30.032 25591-25591 EmailAPI                com.salesmobile                      D  JSON enviado: {"IDCOMPROBANTE":"57677","CODPOS":"MOV1-SUC33","CODEMP":"1","CODSUC":"33","EMAIL":"true","PDF":"false","TEST":"true","TESTPC":"192.168.108.56"}
2025-07-30 15:01:30.041 25591-28817 DB                      com.salesmobile                      D  Actualizado correctamente. ID: 1
2025-07-30 15:01:30.048 25591-28817 DB                      com.salesmobile                      D  Total ventas en BD: 1
2025-07-30 15:01:30.049 25591-28818 ApiClient               com.salesmobile                      D  Realizando POST con Basic Auth: http://192.168.108.56/appmovil/api_testing/enviarEmail.php
2025-07-30 15:01:30.564 25591-28818 ApiClient               com.salesmobile                      D  POST exitoso
2025-07-30 15:01:30.564 25591-28818 EmailAPI                com.salesmobile                      D  Email API exitoso: {
    "success": true,
    "message": "Email será enviado correctamente",
    "data": {
        "destinatario": "<EMAIL>",
        "asunto": "Comprobante Electrónico",
        "email_enviado": true,
        "procesamiento": "background",
        "pdfBase64": null,
        "email_valido": true
    }
}
2025-07-30 15:01:30.564 25591-28818 EmailAPI                com.salesmobile                      D  Email enviado exitosamente: {
    "success": true,
    "message": "Email será enviado correctamente",
    "data": {
        "destinatario": "<EMAIL>",
        "asunto": "Comprobante Electrónico",
        "email_enviado": true,
        "procesamiento": "background",
        "pdfBase64": null,
        "email_valido": true
    }
}
2025-07-30 15:01:30.566 25591-25591 FragmentNavigator       com.salesmobile                      I  Ignoring navigate() call: FragmentManager has already saved its state
2025-07-30 15:01:31.138 25591-27463 AppScoutStateMachine    com.salesmobile                      D  25591-ScoutStateMachinecreated
2025-07-30 15:01:31.162 25591-25591 ForceDarkHelperStubImpl com.salesmobile                      I  setViewRootImplForceDark: false for com.salesmobile.MainActivity@226e5fe, reason: AppDarkModeEnable
2025-07-30 15:01:31.166 25591-25591 BufferQueueConsumer     com.salesmobile                      D  [](id:63f700000009,api:0,p:-1,c:25591) connect: controlledByApp=false
2025-07-30 15:01:31.167 25591-25591 VRI[MainActivity]       com.salesmobile                      D  vri.reportNextDraw android.view.ViewRootImpl.performTraversals:4376 android.view.ViewRootImpl.doTraversal:2992 android.view.ViewRootImpl$TraversalRunnable.run:10469 android.view.Choreographer$CallbackRecord.run:1671 android.view.Choreographer$CallbackRecord.run:1680 
2025-07-30 15:01:31.167 25591-27442 OpenGLRenderer          com.salesmobile                      E  Unable to match the desired swap behavior.
2025-07-30 15:01:31.167 25591-25591 VRI[MainActivity]       com.salesmobile                      D  vri.Setup new sync=wmsSync-VRI[MainActivity]#26
2025-07-30 15:01:31.181 25591-27442 BLASTBufferQueue        com.salesmobile                      D  [VRI[MainActivity]#9](f:0,a:1) acquireNextBufferLocked size=1080x2400 mFrameNumber=1 applyTransaction=true mTimestamp=611092061630039(auto) mPendingTransactions.size=0 graphicBufferId=109912508071975 transform=0
2025-07-30 15:01:31.182 25591-25591 VRI[MainActivity]       com.salesmobile                      D  vri.reportDrawFinished
2025-07-30 15:01:31.194 25591-25591 HandWritingStubImpl     com.salesmobile                      I  refreshLastKeyboardType: 1
2025-07-30 15:01:31.198 25591-25591 HandWritingStubImpl     com.salesmobile                      I  getCurrentKeyboardType: 1
