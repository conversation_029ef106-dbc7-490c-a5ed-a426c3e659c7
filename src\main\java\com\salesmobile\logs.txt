2025-07-30 14:53:49.045 19455-19455 EmailAPI                com.salesmobile                      D  JSON enviado: {"IDCOMPROBANTE":"57676","CODPOS":"MOV1-SUC33","CODEMP":"1","CODSUC":"33","EMAIL":"true","PDF":"false","TEST":"true","TESTPC":"192.168.108.56"}
2025-07-30 14:53:49.051 19455-25216 DB                      com.salesmobile                      D  Actualizado correctamente. ID: 1
2025-07-30 14:53:49.054 19455-25217 ApiClient               com.salesmobile                      D  Realizando POST con Basic Auth: http://192.168.108.56/appmovil/api_testing/enviarEmail.php
2025-07-30 14:53:49.055 19455-25216 DB                      com.salesmobile                      D  Total ventas en BD: 1
2025-07-30 14:53:49.375 19455-25217 ApiClient               com.salesmobile                      D  POST exitoso
2025-07-30 14:53:49.375 19455-25217 EmailAPI                com.salesmobile                      D  Email API exitoso: {
    "success": true,
    "message": "Email será enviado correctamente",
    "data": {
        "destinatario": "<EMAIL>",
        "asunto": "Comprobante Electrónico",
        "email_enviado": true,
        "procesamiento": "background",
        "pdfBase64": null,
        "email_valido": true
    }
}
2025-07-30 14:53:49.375 19455-25217 EmailAPI                com.salesmobile                      D  Email enviado exitosamente: {
    "success": true,
    "message": "Email será enviado correctamente",
    "data": {
        "destinatario": "<EMAIL>",
        "asunto": "Comprobante Electrónico",
        "email_enviado": true,
        "procesamiento": "background",
        "pdfBase64": null,
        "email_valido": true
    }
}
2025-07-30 14:53:49.377 19455-25217 FragmentNavigator       com.salesmobile                      I  Ignoring navigate() call: FragmentManager has already saved its state
2025-07-30 14:53:49.386 19455-25217 ApiClient               com.salesmobile                      E  Error: Method setCurrentState must be called on the main thread (Ask Gemini)
java.lang.IllegalStateException: Method setCurrentState must be called on the main thread
	at androidx.lifecycle.LifecycleRegistry.enforceMainThreadIfNeeded(LifecycleRegistry.jvm.kt:297)
	at androidx.lifecycle.LifecycleRegistry.setCurrentState(LifecycleRegistry.jvm.kt:101)
	at androidx.navigation.NavBackStackEntry.updateState(NavBackStackEntry.kt:188)
	at androidx.navigation.NavBackStackEntry.setMaxLifecycle(NavBackStackEntry.kt:159)
	at androidx.navigation.NavController.updateBackStackLifecycle$navigation_runtime_release(NavController.kt:1090)
	at androidx.navigation.NavController.navigate(NavController.kt:1884)
	at androidx.navigation.NavController.navigate(NavController.kt:1696)
	at androidx.navigation.NavController.navigate(NavController.kt:1609)
	at androidx.navigation.NavController.navigate(NavController.kt:1591)
	at androidx.navigation.NavController.navigate(NavController.kt:1574)
	at com.salesmobile.ui.client.selectClientFragment$5$1.onSuccess(selectClientFragment.java:1029)
	at com.salesmobile.utils.enviarEmail$1.onSuccess(enviarEmail.java:79)
	at com.salesmobile.ApiClient.lambda$postWithBasicAuth$0$com-salesmobile-ApiClient(ApiClient.java:80)
	at com.salesmobile.ApiClient$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)
2025-07-30 14:53:49.387 19455-25217 EmailAPI                com.salesmobile                      D  Error en email API: Error: Method setCurrentState must be called on the main thread
2025-07-30 14:53:49.387 19455-25217 EmailAPI                com.salesmobile                      E  Error enviando email: Error: Method setCurrentState must be called on the main thread
2025-07-30 14:53:49.389 19455-25217 FragmentNavigator       com.salesmobile                      I  Ignoring navigate() call: FragmentManager has already saved its state
2025-07-30 14:53:49.396 19455-25217 AndroidRuntime          com.salesmobile                      E  FATAL EXCEPTION: pool-12-thread-1 (Ask Gemini)
Process: com.salesmobile, PID: 19455
java.lang.IllegalStateException: Method setCurrentState must be called on the main thread
	at androidx.lifecycle.LifecycleRegistry.enforceMainThreadIfNeeded(LifecycleRegistry.jvm.kt:297)
	at androidx.lifecycle.LifecycleRegistry.setCurrentState(LifecycleRegistry.jvm.kt:101)
	at androidx.navigation.NavBackStackEntry.updateState(NavBackStackEntry.kt:188)
	at androidx.navigation.NavBackStackEntry.setMaxLifecycle(NavBackStackEntry.kt:159)
	at androidx.navigation.NavController.updateBackStackLifecycle$navigation_runtime_release(NavController.kt:1090)
	at androidx.navigation.NavController.navigate(NavController.kt:1884)
	at androidx.navigation.NavController.navigate(NavController.kt:1696)
	at androidx.navigation.NavController.navigate(NavController.kt:1609)
	at androidx.navigation.NavController.navigate(NavController.kt:1591)
	at androidx.navigation.NavController.navigate(NavController.kt:1574)
	at com.salesmobile.ui.client.selectClientFragment$5$1.onError(selectClientFragment.java:1037)
	at com.salesmobile.utils.enviarEmail$1.onError(enviarEmail.java:87)
	at com.salesmobile.ApiClient.lambda$postWithBasicAuth$0$com-salesmobile-ApiClient(ApiClient.java:90)
	at com.salesmobile.ApiClient$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)
2025-07-30 14:53:49.398 19455-19455 pool-12-thread-         com.salesmobile                      W  type=1400 audit(0.0:636639): avc:  denied  { search } for  name="miuilog" dev="dm-52" ino=328060 scontext=u:r:untrusted_app:s0:c41,c259,c512,c768 tcontext=u:object_r:data_log_file:s0 tclass=dir permissive=0 app=com.salesmobile
2025-07-30 14:53:49.398 19455-19455 pool-12-thread-         com.salesmobile                      W  type=1400 audit(0.0:636640): avc:  denied  { search } for  name="miuilog" dev="dm-52" ino=328060 scontext=u:r:untrusted_app:s0:c41,c259,c512,c768 tcontext=u:object_r:data_log_file:s0 tclass=dir permissive=0 app=com.salesmobile
2025-07-30 14:53:49.398 19455-19455 pool-12-thread-         com.salesmobile                      W  type=1400 audit(0.0:636641): avc:  denied  { search } for  name="miuilog" dev="dm-52" ino=328060 scontext=u:r:untrusted_app:s0:c41,c259,c512,c768 tcontext=u:object_r:data_log_file:s0 tclass=dir permissive=0 app=com.salesmobile
2025-07-30 14:53:49.398 19455-19455 pool-12-thread-         com.salesmobile                      W  type=1400 audit(0.0:636642): avc:  denied  { search } for  name="miuilog" dev="dm-52" ino=328060 scontext=u:r:untrusted_app:s0:c41,c259,c512,c768 tcontext=u:object_r:data_log_file:s0 tclass=dir permissive=0 app=com.salesmobile
2025-07-30 14:53:49.398 19455-19455 pool-12-thread-         com.salesmobile                      W  type=1400 audit(0.0:636643): avc:  denied  { search } for  name="miuilog" dev="dm-52" ino=328060 scontext=u:r:untrusted_app:s0:c41,c259,c512,c768 tcontext=u:object_r:data_log_file:s0 tclass=dir permissive=0 app=com.salesmobile
2025-07-30 14:53:49.398 19455-19455 pool-12-thread-         com.salesmobile                      W  type=1400 audit(0.0:636644): avc:  denied  { search } for  name="miuilog" dev="dm-52" ino=328060 scontext=u:r:untrusted_app:s0:c41,c259,c512,c768 tcontext=u:object_r:data_log_file:s0 tclass=dir permissive=0 app=com.salesmobile
2025-07-30 14:53:49.398 19455-19455 pool-12-thread-         com.salesmobile                      W  type=1400 audit(0.0:636645): avc:  denied  { getattr } for  path="/data/miuilog" dev="dm-52" ino=328060 scontext=u:r:untrusted_app:s0:c41,c259,c512,c768 tcontext=u:object_r:data_log_file:s0 tclass=dir permissive=0 app=com.salesmobile
2025-07-30 14:53:49.402 19455-19455 pool-12-thread-         com.salesmobile                      W  type=1400 audit(0.0:636646): avc:  denied  { search } for  name="miuilog" dev="dm-52" ino=328060 scontext=u:r:untrusted_app:s0:c41,c259,c512,c768 tcontext=u:object_r:data_log_file:s0 tclass=dir permissive=0 app=com.salesmobile
2025-07-30 14:53:49.406 19455-25217 ScoutUtils              com.salesmobile                      W  Failed to mkdir /data/miuilog/stability/memleak/heapdump/
2025-07-30 14:53:49.443 19455-19455 WindowOnBackDispatcher  com.salesmobile                      W  sendCancelIfRunning: isInProgress=falsecallback=android.app.Activity$$ExternalSyntheticLambda0@3630e
2025-07-30 14:53:49.448 19455-25217 Process                 com.salesmobile                      I  Process is going to kill itself! (Ask Gemini)
java.lang.Exception
	at android.os.Process.killProcess(Process.java:1346)
	at com.android.internal.os.RuntimeInit$KillApplicationHandler.uncaughtException(RuntimeInit.java:178)
	at java.lang.ThreadGroup.uncaughtException(ThreadGroup.java:1071)
	at java.lang.ThreadGroup.uncaughtException(ThreadGroup.java:1066)
	at java.lang.Thread.dispatchUncaughtException(Thread.java:2306)
2025-07-30 14:53:49.448 19455-25217 Process                 com.salesmobile                      I  Sending signal. PID: 19455 SIG: 9
---------------------------- PROCESS ENDED (19455) for package com.salesmobile ----------------------------
---------------------------- PROCESS STARTED (23678) for package com.salesmobile ----------------------------
2025-07-30 14:54:17.512 23678-23678 Compatibil...geReporter com.salesmobile                      D  Compat change id reported: 242716250; UID 10809; state: ENABLED
2025-07-30 14:54:17.535 23678-23678 ziparchive              com.salesmobile                      W  Unable to open '/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes6.dm': No such file or directory
2025-07-30 14:54:17.535 23678-23678 ziparchive              com.salesmobile                      W  Unable to open '/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes13.dm': No such file or directory
2025-07-30 14:54:17.536 23678-23678 ziparchive              com.salesmobile                      W  Unable to open '/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes12.dm': No such file or directory
2025-07-30 14:54:17.536 23678-23678 ziparchive              com.salesmobile                      W  Unable to open '/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes15.dm': No such file or directory
2025-07-30 14:54:17.537 23678-23678 ziparchive              com.salesmobile                      W  Unable to open '/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes4.dm': No such file or directory
2025-07-30 14:54:17.537 23678-23678 ziparchive              com.salesmobile                      W  Unable to open '/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes7.dm': No such file or directory
2025-07-30 14:54:17.538 23678-23678 ziparchive              com.salesmobile                      W  Unable to open '/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes3.dm': No such file or directory
2025-07-30 14:54:17.538 23678-23678 ziparchive              com.salesmobile                      W  Unable to open '/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes11.dm': No such file or directory
2025-07-30 14:54:17.540 23678-23678 com.salesmobile         com.salesmobile                      W  ClassLoaderContext classpath size mismatch. expected=0, found=8 (PCL[] | PCL[/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes6.dex*1947934134:/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes13.dex*1797191952:/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes12.dex*1214484855:/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes15.dex*3069632783:/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes4.dex*535861078:/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes7.dex*3968044348:/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes3.dex*575218285:/data/data/com.salesmobile/code_cache/.overlay/base.apk/classes11.dex*516841895])
2025-07-30 14:54:17.550 23678-23678 nativeloader            com.salesmobile                      D  Configuring clns-4 for other apk /data/app/~~k-WghPMDf-p9yTZf7FkXkg==/com.salesmobile-FczFfyGL0nyUC-HpAq9ENQ==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~k-WghPMDf-p9yTZf7FkXkg==/com.salesmobile-FczFfyGL0nyUC-HpAq9ENQ==/lib/arm64:/data/app/~~k-WghPMDf-p9yTZf7FkXkg==/com.salesmobile-FczFfyGL0nyUC-HpAq9ENQ==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.salesmobile
2025-07-30 14:54:17.559 23678-23678 nativeloader            com.salesmobile                      D  Load libframework-connectivity-jni.so using APEX ns com_android_tethering for caller /apex/com.android.tethering/javalib/framework-connectivity.jar: ok
2025-07-30 14:54:17.562 23678-23678 GraphicsEnvironment     com.salesmobile                      V  Currently set values for:
2025-07-30 14:54:17.562 23678-23678 GraphicsEnvironment     com.salesmobile                      V    angle_gl_driver_selection_pkgs=[]
2025-07-30 14:54:17.562 23678-23678 GraphicsEnvironment     com.salesmobile                      V    angle_gl_driver_selection_values=[]
2025-07-30 14:54:17.562 23678-23678 GraphicsEnvironment     com.salesmobile                      V  ANGLE GameManagerService for com.salesmobile: false
2025-07-30 14:54:17.562 23678-23678 GraphicsEnvironment     com.salesmobile                      V  com.salesmobile is not listed in per-application setting
2025-07-30 14:54:17.563 23678-23678 GraphicsEnvironment     com.salesmobile                      V  App is not on the allowlist for updatable production driver.
2025-07-30 14:54:17.566 23678-23678 ForceDarkHelperStubImpl com.salesmobile                      I  initialize for com.salesmobile , ForceDarkAppConfig: null
2025-07-30 14:54:17.567 23678-23678 nativeloader            com.salesmobile                      D  Load libforcedarkimpl.so using system ns (caller=/system_ext/framework/miui-framework.jar): ok
2025-07-30 14:54:17.568 23678-23678 OpenGLRenderer          com.salesmobile                      D  JNI_OnLoad success
2025-07-30 14:54:17.568 23678-23678 MiuiForceDarkConfig     com.salesmobile                      I  setConfig density:2.750000, mainRule:0, secondaryRule:0, tertiaryRule:0
2025-07-30 14:54:17.578 23678-23678 Compatibil...geReporter com.salesmobile                      D  Compat change id reported: 183155436; UID 10809; state: ENABLED
2025-07-30 14:54:17.584 23678-26218 skia                    com.salesmobile                      D  SkJpegCodec::onGetPixels + (512, 512)
2025-07-30 14:54:17.585 23678-23678 FirebaseApp             com.salesmobile                      W  Default FirebaseApp failed to initialize because no default options were found. This usually means that com.google.gms:google-services was not applied to your gradle project.
2025-07-30 14:54:17.585 23678-23678 FirebaseInitProvider    com.salesmobile                      I  FirebaseApp initialization unsuccessful
2025-07-30 14:54:17.586 23678-26218 skia                    com.salesmobile                      D  SkJpegCodec::onGetPixels -
2025-07-30 14:54:17.586 23678-26218 skia                    com.salesmobile                      D  onGetGainmapInfo: false.
2025-07-30 14:54:17.608 23678-23678 M-ProMotion             com.salesmobile                      I  M-ProMotion is disabled
2025-07-30 14:54:17.612 23678-26226 libMEOW                 com.salesmobile                      D  meow new tls: 0xb400007df9f55040
2025-07-30 14:54:17.613 23678-26226 libMEOW                 com.salesmobile                      D  meow reload base cfg path: na
2025-07-30 14:54:17.613 23678-26226 libMEOW                 com.salesmobile                      D  meow reload overlay cfg path: na
2025-07-30 14:54:17.613 23678-26226 QT                      com.salesmobile                      W  qt_process_init() called
2025-07-30 14:54:17.613 23678-26226 QT                      com.salesmobile                      E  [QT]file does not exist