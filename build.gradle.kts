plugins {
    alias(libs.plugins.androidApplication)
}

android {
    namespace = "com.salesmobile"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.salesmobile"
        minSdk = 33
        targetSdk = 34
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    buildFeatures {
        viewBinding = true
    }

    packaging {
        jniLibs {
            useLegacyPackaging = false
        }
    }
}

dependencies {
    implementation(libs.volley)
    implementation(libs.tracing.perfetto.handshake)
    implementation(libs.media3.common)
    implementation(libs.appcompat);
    implementation(libs.material);
    implementation(libs.constraintlayout);
    implementation(libs.lifecycle.livedata.ktx);
    implementation(libs.lifecycle.viewmodel.ktx);
    implementation(libs.navigation.fragment);
    implementation(libs.navigation.ui);
    implementation(libs.legacy.support.v4)
    implementation(libs.annotation)
    implementation(libs.recyclerview);
    testImplementation(libs.junit);
    androidTestImplementation(libs.ext.junit);
    androidTestImplementation(libs.espresso.core);
    implementation(libs.retrofit);
    implementation(libs.converter.gson);
    implementation(libs.logging.interceptor);
    implementation(libs.barcode.scanning);
    implementation(libs.camera.core);
    implementation(libs.camera.camera2);
    implementation(libs.camera.lifecycle);
    implementation(libs.camera.view);

    // Room
    implementation(libs.room.runtime);
    annotationProcessor(libs.room.compiler);
    implementation (libs.stetho);

    // Para el fragmento
    implementation (libs.fragment);

    // Material Design Components
    implementation (libs.material.v150);
    implementation (libs.kotlinx.coroutines.android);
    implementation (libs.lifecycle.runtime.ktx);
    implementation (libs.jtds);
    implementation (libs.firebase.messaging);
    implementation (libs.retrofit.v290);
    implementation (libs.converter.gson.v290);
    implementation (libs.glide);
    annotationProcessor (libs.compiler);
    implementation (libs.zxing.android.embedded);
    implementation (libs.core)
    implementation (libs.glide.v4120);
    implementation(libs.okhttp);
    implementation (libs.gson);

}