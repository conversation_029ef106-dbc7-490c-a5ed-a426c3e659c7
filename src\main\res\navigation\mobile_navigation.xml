<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@id/nav_home">

    <action
        android:id="@+id/action_nav_pay_to_carritoFragment"
        app:destination="@id/nav_inputProduct"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_right"
        app:popExitAnim="@anim/slide_out_left" />

    <action
        android:id="@+id/action_nav_pay_to_home"
        app:destination="@id/nav_home"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_right"
        app:popExitAnim="@anim/slide_out_left" />


    <action
        android:id="@+id/action_nav_inputProduct_to_carritoFragment"
        app:destination="@id/nav_carritoFragment"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_right"
        app:popExitAnim="@anim/slide_out_left" />

    <action
        android:id="@+id/action_nav_carritoFragment_to_inputProduct"
        app:destination="@id/nav_inputProduct"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_right"
        app:popExitAnim="@anim/slide_out_left" />

    <action
        android:id="@+id/action_nav_carritoFragment_to_fragment_select_client"
        app:destination="@id/nav_selectClientFragment"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_right"
        app:popExitAnim="@anim/slide_out_left" />

    <action
        android:id="@+id/action_nav_clientFragment"
        app:destination="@id/nav_clientFragment" />

    <action
        android:id="@+id/action_nav_clientFragment_to_selectClientFragment"
        app:destination="@id/nav_selectClientFragment" />

    <action
        android:id="@+id/action_to_Fragment_pay"
        app:destination="@id/payFragment" />

    <action
        android:id="@+id/action_configuracion"
        app:destination="@id/nav_configuracion" />
    <action
        android:id="@+id/action_stcok"
        app:destination="@id/nav_stock" />

    <!-- Fragmento de Stock -->
    <fragment
        android:id="@+id/nav_stock"
        android:name="com.salesmobile.ui.stock.StockFragment"
        android:label="Stock"
        tools:layout="@layout/fragment_stock" />
    <!-- Fragmento de Configuración -->
    <fragment
        android:id="@+id/nav_configuracion"
        android:name="com.salesmobile.fragment_configuracion"
        android:label="Configuración" />
    <fragment
        android:id="@+id/nav_home"
        android:name="com.salesmobile.ui.home.HomeFragment"
        android:label="@string/menu_home"
        tools:layout="@layout/fragment_home" />
    <fragment
        android:id="@+id/nav_inputProduct"
        android:name="com.salesmobile.ui.inputproduct.InputProductFragment"
        android:label="@string/menu_inputProduct"
        tools:layout="@layout/fragment_inputproduct" />


    <fragment
        android:id="@+id/nav_carritoFragment"
        android:name="com.salesmobile.ui.inputproduct.CarritoFragment"
        android:label="Carrito"
        tools:layout="@layout/fragment_carrito" />

    <fragment
        android:id="@+id/nav_slideshow"
        android:name="com.salesmobile.ui.slideshow.SlideshowFragment"
        android:label="@string/menu_slideshow"
        tools:layout="@layout/fragment_slideshow" />
    <fragment
        android:id="@+id/nav_selectClientFragment"
        android:name="com.salesmobile.ui.client.selectClientFragment"
        android:label="Seleccionar Cliente"
        tools:layout="@layout/fragment_select_client" />
    <fragment
        android:id="@+id/nav_clientFragment"
        android:name="com.salesmobile.ui.client.ClientFragment"
        android:label="@string/menu_client"
        tools:layout="@layout/fragment_client" />
    <fragment
        android:id="@+id/payFragment"
        android:name="com.salesmobile.ui.pay.payFragment"
        android:label="Cobranza"
        tools:layout="@layout/fragment_pay" />
</navigation>