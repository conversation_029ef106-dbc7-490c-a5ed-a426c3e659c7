<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/white"
    tools:context="ui.login.LoginActivity">

    <!-- Contenedor principal del formulario con peso flexible -->
    <LinearLayout
        android:id="@+id/login_form"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="visible">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="24dp"
            android:src="@drawable/logosalesmovile"
            android:scaleType="centerCrop"
            android:contentDescription="@string/salesmobile" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/aplicaci_n_de_ventas"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_marginBottom="32dp"
            android:textStyle="bold"
            android:layout_gravity="center_horizontal"/>

        <!-- Usuario SQL Server -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etUsuarioSQL"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/c_digo_de_vendedor"
                android:inputType="text"
                android:maxLines="1"/>
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Password SQL Server -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etPasswordSQL"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Contraseña Vendedor"
                android:inputType="textPassword"
                android:maxLines="1"/>
        </com.google.android.material.textfield.TextInputLayout>

        <CheckBox
            android:id="@+id/cbRecordar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Recordar contraseña"
            android:layout_marginBottom="16dp"
            tools:ignore="HardcodedText" />

        <Button
            android:id="@+id/btnLogin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/conectar"
            android:textColor="@color/white"
            android:backgroundTint="@color/purple_500"
            android:layout_marginBottom="16dp"/>

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:visibility="gone"/>
    </LinearLayout>

    <!-- Contenedor para el fragmento de configuración -->
    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:visibility="gone"/>

    <!-- Botón de Configuración siempre visible en la parte inferior -->
    <Button
        android:id="@+id/btnConfiguracion"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:backgroundTint="@color/gray"
        android:text="Configurar Conexión"
        android:textColor="#263238"
        tools:ignore="HardcodedText,VisualLintButtonSize" />

</LinearLayout>