package com.salesmobile.ui.articulos;


import android.content.Context;
import android.widget.ArrayAdapter;
import android.widget.ListView;

import java.util.ArrayList;
import java.util.List;

public class ViewArticulos {
    private String nombre;
    private int cantidad;

    public String getNombre() {
        return nombre;
    }
    public int getCantidad() {
        return cantidad;
    }

    public ViewArticulos(String nombre, int cantidad) {
        this.nombre = nombre;
        this.cantidad = cantidad;
    }

    public static void cargarArticulos(Context context, ListView listView) {
        List<ViewArticulos> listaArticulos = new ArrayList<>();
        listaArticulos.add(new ViewArticulos("Remera", 1));
        listaArticulos.add(new ViewArticulos("Kepi", 2));
        // Agrega más artículos según sea necesario

        String[] nombresArticulos = new String[listaArticulos.size()];
        for (int i = 0; i < listaArticulos.size(); i++) {
            ViewArticulos articulo = listaArticulos.get(i);
            nombresArticulos[i] = articulo.getNombre() + " cantidad " + articulo.getCantidad();
        }

        ArrayAdapter<String> adapter = new ArrayAdapter<>(context, android.R.layout.simple_list_item_1, nombresArticulos);
        listView.setAdapter(adapter);
    }
}
