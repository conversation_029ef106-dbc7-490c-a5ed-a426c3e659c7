package com.salesmobile.ui.home;

import android.util.Log;
import android.widget.Toast;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.salesmobile.AppDatabase;
import com.salesmobile.ui.factura.Venta;

import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.Executors;

public class HomeViewModel extends ViewModel {
    private final MutableLiveData<String> mFecha = new MutableLiveData<>();
    private final MutableLiveData<String> mTotalVentas = new MutableLiveData<>();
    private final MutableLiveData<String> mTransacciones = new MutableLiveData<>();
    private final MutableLiveData<String> mTicketMedio = new MutableLiveData<>();
    private final MutableLiveData<String> mTendencia = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>(true);

    public HomeViewModel() {
        // Formatear fecha actual
        SimpleDateFormat dateFormat = new SimpleDateFormat("EEEE, d 'de' MMMM", Locale.getDefault());
        mFecha.setValue(dateFormat.format(new Date()));
        mTotalVentas.setValue("Gs. 0.00");
        mTransacciones.setValue("0");
        mTicketMedio.setValue("Gs. 0.00");
        mTendencia.setValue("+0% respecto a ayer");
    }

    // Getters para todos los LiveData...
    public LiveData<String> getTotalVentas() {
        return mTotalVentas;
    }

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }
    // Método para obtener la fecha formateada
    public LiveData<String> getFecha() {
        return mFecha;
    }

    public LiveData<String> getTransacciones() {
        return mTransacciones;
    }

    public LiveData<String> getTicketMedio() {
        return mTicketMedio;
    }

    public void cargarDatosDelDia(AppDatabase db) {
        isLoading.setValue(true);
        Locale localePY = new Locale("es", "PY");
        Executors.newSingleThreadExecutor().execute(() -> {
            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
                String fecha = dateFormat.format(new Date());

                Venta venta = db.ventaDao().getVentaPorFecha(fecha);
                NumberFormat formatter = NumberFormat.getNumberInstance(localePY);

                if (venta != null) {
                    // Usar getters en lugar de acceso directo a campos
                    int transacciones = venta.getNumTransacciones();
                    double totalVenta = venta.getTotal();

                    Log.d("HomeViewModel", "Transacciones encontradas: " + transacciones);
                    mTotalVentas.postValue(formatter.format(totalVenta));
                    mTransacciones.postValue(String.valueOf(transacciones));

                    if (transacciones > 0) {
                        mTicketMedio.postValue(formatter.format(totalVenta / transacciones));
                    } else {
                        mTicketMedio.postValue(formatter.format(0));
                    }

                    Log.d("HomeViewModel", "Total ventas: " + totalVenta +
                            ", Cant. Ventas: " + transacciones +
                            ", Ventas Promedio: " + (transacciones > 0 ? totalVenta / transacciones : 0));

                    mTendencia.postValue(calcularTendencia(db, fecha));
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                isLoading.postValue(false);
            }
        });
    }

    private String calcularTendencia(AppDatabase db, String fechaActual) {
        // Implementar lógica para comparar con el día anterior
        return "+12% respecto a ayer";
    }
}