package com.salesmobile;

import android.content.Context;

public class SucursalUtils {
    
    public static SucursalInfoManager.SucursalInfo getSucursalInfo(Context context) {
        return SucursalInfoManager.getSucursalInfoGuardada(context);
    }
    
    public static String getNombreSucursal(Context context) {
        SucursalInfoManager.SucursalInfo info = getSucursalInfo(context);
        return info != null ? info.getNombre() : "";
    }
    
    public static String getDomicilioSucursal(Context context) {
        SucursalInfoManager.SucursalInfo info = getSucursalInfo(context);
        return info != null ? info.getDomicilio() : "";
    }
    
    public static String getDomicilio2Sucursal(Context context) {
        SucursalInfoManager.SucursalInfo info = getSucursalInfo(context);
        return info != null ? info.getDomicilio2() : "";
    }
    
    public static String getLocalidadSucursal(Context context) {
        SucursalInfoManager.SucursalInfo info = getSucursalInfo(context);
        return info != null ? info.getLocalidad() : "";
    }
    
    public static String getTelefonoSucursal(Context context) {
        SucursalInfoManager.SucursalInfo info = getSucursalInfo(context);
        return info != null ? info.getTelefono() : "";
    }
    
    public static int getCodigoSucursal(Context context) {
        SucursalInfoManager.SucursalInfo info = getSucursalInfo(context);
        return info != null ? info.getCodsuc() : 0;
    }
    
    public static boolean haySucursalInfo(Context context) {
        return getSucursalInfo(context) != null;
    }
    
    public static String getDireccionCompleta(Context context) {
        SucursalInfoManager.SucursalInfo info = getSucursalInfo(context);
        if (info != null) {
            String direccion = info.getDomicilio();
            if (!info.getDomicilio2().isEmpty()) {
                direccion += ", " + info.getDomicilio2();
            }
            return direccion;
        }
        return "";
    }
    
    public static void limpiarSucursalInfo(Context context) {
        SucursalInfoManager.limpiarSucursalInfo(context);
    }
}
