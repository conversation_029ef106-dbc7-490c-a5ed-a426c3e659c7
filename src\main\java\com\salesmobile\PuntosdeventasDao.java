package com.salesmobile;



import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

@Dao
public interface PuntosdeventasDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE) // Reemplaza el registro si ya existe
    void insert(Puntosdeventas puntosdeventas);

    @Update
    void update(Puntosdeventas puntosdeventas);

    @Query("SELECT * FROM puntosdeventas LIMIT 1")
    Puntosdeventas obtenerPuntosdeventas();

    @Query("DELETE FROM puntosdeventas")
    void eliminarPuntosdeventas();
}