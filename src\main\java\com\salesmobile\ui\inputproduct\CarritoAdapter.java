package com.salesmobile.ui.inputproduct;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.salesmobile.R;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class CarritoAdapter extends RecyclerView.Adapter<CarritoAdapter.ProductoViewHolder> {
    private List<Product> productos;
    private OnItemClickListener listener;

    public interface OnItemClickListener {
        void onDeleteClick(int position);
    }

    public CarritoAdapter(List<Product> productos, OnItemClickListener listener) {
        this.productos = productos;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ProductoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_carrito, parent, false);
        return new ProductoViewHolder(view, listener);
    }

    @Override
    public void onBindViewHolder(@NonNull ProductoViewHolder holder, int position) {
        Product producto = productos.get(position);
        holder.bind(producto);
    }

    @Override
    public int getItemCount() {
        return productos.size();
    }

    public void updateList(List<Product> newList) {
        productos = new ArrayList<>(newList);
        notifyDataSetChanged();
    }

    static class ProductoViewHolder extends RecyclerView.ViewHolder {
        private TextView nombre, precio, cantidad, talla;
        private ImageButton btnEliminar;

        public ProductoViewHolder(@NonNull View itemView, OnItemClickListener listener) {
            super(itemView);
            nombre = itemView.findViewById(R.id.productName);
            precio = itemView.findViewById(R.id.productPrice);
            cantidad = itemView.findViewById(R.id.productQuantity);
            talla = itemView.findViewById(R.id.productSize);
            btnEliminar = itemView.findViewById(R.id.btnRemove);

            btnEliminar.setOnClickListener(v -> {
                if (listener != null && getAdapterPosition() != RecyclerView.NO_POSITION) {
                    listener.onDeleteClick(getAdapterPosition());
                }
            });
        }

        public void bind(Product producto) {
            nombre.setText(producto.getName());

            // Formatear precio con formato paraguayo
            NumberFormat format = NumberFormat.getInstance(new Locale("es", "PY"));
            format.setMaximumFractionDigits(0); // sin decimales
            String precioFormateado = "Gs. " + format.format(producto.getPrice());
            precio.setText(precioFormateado);

            cantidad.setText(String.format("Cant: %s", producto.getQuantity()));
            talla.setText(String.format("Talla: %s", producto.getSize()));
        }
    }
}