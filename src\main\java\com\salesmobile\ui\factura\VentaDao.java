package com.salesmobile.ui.factura;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

@Dao
public interface VentaDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(Venta venta); // Retorna el ID del nuevo registro

    @Query("SELECT * FROM ventas ORDER BY fecha DESC")
    List<Venta> getAllVentas();

    @Query("SELECT SUM(total) FROM ventas WHERE fecha = :fecha")
    double getTotalPorFecha(String fecha);

    @Query("SELECT * FROM ventas WHERE fecha = :fecha LIMIT 1")
    Venta getVentaPorFecha(String fecha);


    @Update
    int update(Venta venta);
}