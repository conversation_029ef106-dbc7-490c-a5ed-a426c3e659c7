package com.salesmobile.ui.factura;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import androidx.room.Room;

import com.android.volley.*;
import com.android.volley.toolbox.HttpHeaderParser;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.salesmobile.AppDatabase;
import com.salesmobile.Configuracion;
import com.salesmobile.Puntosdeventas;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.Executors;

public class FacturaManager {
    private Context context;
    private String token;
    private String baseUrl;
    private String CodEmpresa;
    private String CodSucursal;
    private RequestQueue requestQueue;
    private AppDatabase db;


    public FacturaManager(Context context, String token, String baseUrl, String CodEmpresa, String CodSucursal) {
        this.context = context;
        this.token = token;
        this.baseUrl = baseUrl;
        this.CodEmpresa = CodEmpresa;
        this.CodSucursal = CodSucursal;
        this.requestQueue = Volley.newRequestQueue(context);
        this.db = AppDatabase.getInstance(context);
    }

    private String obtenerPuntoDeVenta() {
        SharedPreferences sharedPreferences = context.getSharedPreferences("ConfigDB", Context.MODE_PRIVATE);
        return sharedPreferences.getString("puntoventa", null);
    }

    private String obtenerPrefijo() {
        SharedPreferences sharedPreferences = context.getSharedPreferences("ConfigDB", Context.MODE_PRIVATE);
        return sharedPreferences.getString("prefijo_puntoventa", null);
    }

    private String obtenerCodigoVendedor() {
        SharedPreferences sharedPreferences = context.getSharedPreferences("ConfigDB", Context.MODE_PRIVATE);
        return sharedPreferences.getString("codigovendedor", null);
    }

    public void grabarFactura(String clienteCodigo, List<Producto> productos, String ticket_number, String merchant_code, String card_last_numbers, String hookAlias, FacturaCallback callback) {
        JSONObject facturaJson = construirFacturaJson(clienteCodigo, productos,ticket_number,merchant_code,card_last_numbers, hookAlias);
        Log.d("FacturaRequest", "JSON a enviar: " + facturaJson.toString());

        double totalVenta = calcularTotalVenta(productos);
        String url = baseUrl + "/API/COMPROBANTEVENTA";

        // Usamos StringRequest en lugar de JsonObjectRequest para tener más control
        StringRequest stringRequest = new StringRequest(
                Request.Method.POST,
                url,
                response -> {
                    // Este caso solo debería ejecutarse para códigos 200
                    try {
                        JSONObject jsonResponse = new JSONObject(response);
                        guardarVentaLocal(totalVenta);
                        callback.onSuccess(jsonResponse);
                    } catch (JSONException e) {
                        callback.onError(new VolleyError("Error al parsear respuesta JSON"));
                    }
                },
                error -> {
                    // Manejar errores de red o respuestas con código != 200
                    // Intentar extraer respuesta del servidor y guardar venta local si es posible
                    if (error.networkResponse != null && error.networkResponse.data != null) {
                        try {
                            String errorBody = new String(error.networkResponse.data, "UTF-8");
                            JSONObject errorJson = new JSONObject(errorBody);
                            // Si la respuesta contiene datos válidos, guardar venta local
                            if (errorJson.has("cuerpo") || errorJson.has("IDCOMPROBANTE")) {
                                guardarVentaLocal(totalVenta);
                            }
                        } catch (Exception e) {
                            // Si no se puede parsear, continuar sin guardar venta local
                            Log.d("FacturaManager", "No se pudo parsear respuesta de error: " + e.getMessage());
                        }
                    }
                    callback.onError(error);
                }) {
            @Override
            public byte[] getBody() throws AuthFailureError {
                try {
                    return facturaJson.toString().getBytes("utf-8");
                } catch (UnsupportedEncodingException e) {
                    return null;
                }
            }

            @Override
            public Map<String, String> getHeaders() throws AuthFailureError {
                Map<String, String> headers = new HashMap<>();
                headers.put("Authorization", "Bearer " + token);
                headers.put("Content-Type", "application/json");
                headers.put("accept", "*/*");
                return headers;
            }

            @Override
            protected Response<String> parseNetworkResponse(NetworkResponse response) {
                String parsed;
                try {
                    parsed = new String(response.data, HttpHeaderParser.parseCharset(response.headers));
                } catch (UnsupportedEncodingException e) {
                    parsed = new String(response.data);
                }

                // Forzar error si el código no es 200
                if (response.statusCode != 200) {
                    return Response.error(new VolleyError("Código HTTP " + response.statusCode + ": " + parsed));
                }

                return Response.success(parsed, HttpHeaderParser.parseCacheHeaders(response));
            }
        };

        requestQueue.getCache().clear();
        requestQueue.add(stringRequest);
    }

    private double calcularTotalVenta(List<Producto> productos) {
        double total = 0;
        for (Producto producto : productos) {
            total += Double.parseDouble(producto.getPrecio()) * Double.parseDouble(producto.getCantidad());
        }
        return total;
    }

    private void guardarVentaLocal(double total) {
        // Obtener fecha actual
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
        String fecha = dateFormat.format(new Date());

        Executors.newSingleThreadExecutor().execute(() -> {
            try {

                Venta ventaExistente = db.ventaDao().getVentaPorFecha(fecha);

                if (ventaExistente != null) {

                    double nuevoTotal = ventaExistente.getTotal() + total;
                    ventaExistente.setTotal(nuevoTotal);
                    ventaExistente.setNumTransacciones(ventaExistente.getNumTransacciones() + 1);
                    int updatedRows = db.ventaDao().update(ventaExistente);

                    if (updatedRows > 0) {
                        Log.d("DB", "Actualizado correctamente. ID: " + ventaExistente.getId());
                    } else {
                        Log.e("DB", "Error al actualizar");
                    }
                } else {
                    // Si no existe, insertamos nueva venta
                    Venta nuevaVenta = new Venta(fecha, total);
                    long rowId = db.ventaDao().insert(nuevaVenta);

                    if (rowId != -1) {
                        // Actualiza el ID generado en el objeto
                        nuevaVenta.setId(rowId);
                        Log.d("DB", "Insertado correctamente. ID: " + nuevaVenta.getId());
                    } else {
                        Log.e("DB", "Error al insertar");
                    }
                }

                // Verificación opcional (puedes comentar esto en producción)
                List<Venta> ventas = db.ventaDao().getAllVentas();
                Log.d("DB", "Total ventas en BD: " + ventas.size());

            } catch (Exception e) {
                Log.e("DB", "Error: " + e.getMessage());
            }
        });
    }
    private JSONObject construirFacturaJson(String clienteCodigo, List<Producto> productos,String ticket_number, String merchant_code, String card_last_numbers, String hookAlias) {
        JSONObject factura = new JSONObject();
        JSONObject header = new JSONObject();
        JSONObject comprobanteVenta = new JSONObject();
        JSONArray itemsArray = new JSONArray();
        JSONArray efectivosArray = new JSONArray();

        try {
            // Formateador de fecha
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
            String fechaActual = dateFormat.format(new Date());

            String puntoVenta = obtenerPuntoDeVenta();
            String prefijo_pasar = obtenerPrefijo();
            String codigoVendedor = obtenerCodigoVendedor();

            // Construir HEADER (en mayúsculas como en el ejemplo)
            header.put("ETIQUETA", "COMPROBANTES DE VENTA");
            header.put("CODEMP", CodEmpresa); // Código de empresa
            header.put("CODSUC", CodSucursal); // Código de sucursal
            header.put("FECHA", fechaActual);

            // Construir ComprobanteVenta
            comprobanteVenta.put("TipoABC", "B");
            comprobanteVenta.put("PuntoVenta", puntoVenta.trim());
            comprobanteVenta.put("Fecha", fechaActual);
            comprobanteVenta.put("Comprobante", "FB");
            comprobanteVenta.put("Prefijo", prefijo_pasar.trim());
            comprobanteVenta.put("Cliente_Codigo", clienteCodigo);
            comprobanteVenta.put("Vendedor_Codigo", codigoVendedor.trim());
            comprobanteVenta.put("Cobrador_Codigo", codigoVendedor.trim());
            comprobanteVenta.put("Operador", "cajerom");
            comprobanteVenta.put("Cajero", "cajerom");
            comprobanteVenta.put("CondicionImpositiva", "F");
            comprobanteVenta.put("CodigoCondicionVenta", "V01");

            // Construir Items
            BigDecimal totalGravado = BigDecimal.ZERO;
            BigDecimal totalIVA = BigDecimal.ZERO;
            BigDecimal total = BigDecimal.ZERO;

            for (Producto producto : productos) {
                JSONObject item = new JSONObject();


                BigDecimal precioUnitario = new BigDecimal(producto.getPrecio());
                BigDecimal cantidad = new BigDecimal(producto.getCantidad());
                BigDecimal iva = new BigDecimal("1.10"); // 10% de IVA
                BigDecimal precioSinIva = precioUnitario.divide(iva, 2, RoundingMode.HALF_UP);
                BigDecimal importeTotal_var = precioUnitario.multiply(cantidad);
                BigDecimal importeGravado_var = precioSinIva.multiply(cantidad);
                BigDecimal importeIva_var = importeTotal_var.subtract(importeGravado_var).setScale(2, RoundingMode.HALF_UP);

                //Formar Codigo + color + talla
                String codigo_var = producto.getCodigo().trim();
                String talla_var = producto.getTalla().trim();
                String codigo_final = codigo_var + "0," + talla_var;

                // item.put("CodigoItem", producto.getCodigo());
                item.put("CodigoItem", codigo_final);
                item.put("NumeroUnidadMedida", "1");
                item.put("CantidadPrimeraUnidad", producto.getCantidad());
                item.put("PrecioUnitario", producto.getPrecio());
                item.put("ImporteGravado", String.format(Locale.US, "%.2f", importeGravado_var));
                item.put("ImporteIva", String.format(Locale.US, "%.2f", importeIva_var));
                item.put("ImportePercepcionIva", "0");
                item.put("ImporteTotal", String.format(Locale.US, "%.2f", importeTotal_var));
                item.put("TasaIva", "10");

                itemsArray.put(item);

                totalGravado = totalGravado.add(importeGravado_var);
                totalIVA = totalIVA.add(importeIva_var);
                total = total.add(importeTotal_var);
            }

            // Construir Tarjeta (pago)
            JSONObject efectivo = new JSONObject();
            efectivo.put("MedioPago", "03");
            efectivo.put("codigoTarjeta", "00");
            efectivo.put("Fecha", fechaActual);
            efectivo.put("numeroTarjeta", card_last_numbers);
            efectivo.put("numeroCupon", ticket_number.substring(1));
            efectivo.put("codigoTerminal", "01");
            efectivo.put("tipoCupon", "C");
            efectivo.put("telefono","");
            efectivo.put("nombre","");
            if (merchant_code.equals("2118")) {
                efectivo.put("plan", "INF");
            } else if (merchant_code.equals("0-301368")) {
                efectivo.put("plan", "BEP");
            } else if (merchant_code.equals("1500481009")) {
                efectivo.put("plan", "PAN");
            } else if (merchant_code.equals("2060503")) {
                efectivo.put("plan", "CAB");
            } else if (merchant_code.equals("2007714")) {
                efectivo.put("plan", "PRO");
            } else if (merchant_code.equals("553149")) {
                efectivo.put("plan", "BAN");
            }
            efectivo.put("Importe", String.format(Locale.US, "%.2f", total));
            efectivosArray.put(efectivo);


            // Completar comprobante
            comprobanteVenta.put("TotalGravado", String.format(Locale.US, "%.2f", totalGravado));
            comprobanteVenta.put("TotalIVA", String.format(Locale.US, "%.2f", totalIVA));
            comprobanteVenta.put("totalNoInscripto", "0");
            comprobanteVenta.put("totalImpuestoInterno", "0");
            comprobanteVenta.put("TotalPercepcionIVA", "0");
            comprobanteVenta.put("totalPercepcionIngresosBrutos", "0");
            comprobanteVenta.put("Total", String.format(Locale.US, "%.2f", total));
            
            String observaciones = "Venta desde app móvil";
            if (hookAlias != null && !hookAlias.isEmpty()) {
                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss", Locale.getDefault());
                String fechaHora = sdf.format(new Date());
                
                observaciones += " - Hookalias: " + hookAlias + " Fecha: " + fechaHora;
            }
            
            comprobanteVenta.put("Observaciones", observaciones);        
            comprobanteVenta.put("Items", itemsArray);
            comprobanteVenta.put("tarjetas", efectivosArray);

            // Construir objeto final
            factura.put("HEADER", header); // En mayúsculas
            factura.put("ComprobanteVenta", comprobanteVenta);

        } catch (JSONException e) {
            Log.e("JSONError", "Error al construir JSON de factura", e);
        }

        return factura;
    }



    public interface FacturaCallback {
        void onSuccess(JSONObject response);
        void onError(VolleyError error);
    }
}