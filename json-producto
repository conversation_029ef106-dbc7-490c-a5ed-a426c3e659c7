{
  "header": {
    "etiqueta": "CONSULTAGRAL",
    "codemp": 1,
    "codsuc": 1,
    "fecha": "12/06/2024"
  },
  "consultaGral": {
   "sql": "SELECT
               CODITM as CodigoTalla,
               LEFT(CODITM, CHARINDEX(',', CODITM) - 1) AS Codigo,
               SUBSTRING(CODITM, CHARINDEX(',', CODITM) + 1, LEN(CODITM) - CHARINDEX(',', CODITM)) AS Talla, STKACTUAL
           FROM PVItemsAcum
           WHERE
               CHARINDEX(',', CODITM) > 0 AND
               LEFT(CODITM, CHARINDEX(',', CODITM) - 1) = '201000';",
    "filtroCodigos": [
      {
        "codigo": "201000,0"
      }
    ],
    "filtrosAdicionales": [
      {
        "tagEntidad": "ListaPrecio",
        "nombreCampo": "Codigo",
        "comparacion": "2",
        "valor": "201000,0"
      }
    ],
    "incluyeDatosSecundarios": "N"
  }
}