package com.salesmobile.ui.home;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.room.Room;

import com.salesmobile.AppDatabase;
import com.salesmobile.IndicesApiClient;
import com.salesmobile.R;
import com.salesmobile.SucursalInfoManager;
import com.salesmobile.TokenManager;
import com.salesmobile.config.SharedPreferences;
import com.salesmobile.databinding.FragmentHomeBinding;

import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class HomeFragment extends Fragment {
    private static final String TAG = "HomeFragment";
    private HomeViewModel homeViewModel;
    private AppDatabase db;
    private SharedPreferences sharedPrefs;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        db = AppDatabase.getInstance(requireContext());
        sharedPrefs = new SharedPreferences(requireContext());
    }

    public View onCreateView(@NonNull LayoutInflater inflater,
                             ViewGroup container, Bundle savedInstanceState) {
        homeViewModel = new ViewModelProvider(this).get(HomeViewModel.class);
        View root = inflater.inflate(R.layout.fragment_home, container, false);

        // Referencias a los elementos de la UI
        final TextView tvNombreSucursal = root.findViewById(R.id.tv_nombre_sucursal);
        final TextView tvFecha = root.findViewById(R.id.tv_fecha);
        final TextView tvTotal = root.findViewById(R.id.tv_total);
        final ProgressBar progressBar = root.findViewById(R.id.progressBar);
        final TextView tvTransacciones = root.findViewById(R.id.tv_transacciones);
        final TextView tvTicketMedio = root.findViewById(R.id.tv_ticket_medio);
        final TextView tvCotizacion = root.findViewById(R.id.tv_cotizacion);

        homeViewModel.getFecha().observe(getViewLifecycleOwner(), tvFecha::setText);
        homeViewModel.getTotalVentas().observe(getViewLifecycleOwner(), tvTotal::setText);
        homeViewModel.getTransacciones().observe(getViewLifecycleOwner(), tvTransacciones::setText);
        homeViewModel.getTicketMedio().observe(getViewLifecycleOwner(), tvTicketMedio::setText);

        homeViewModel.getIsLoading().observe(getViewLifecycleOwner(), isLoading -> {
            if (isLoading) {
                progressBar.setVisibility(View.VISIBLE);
                tvTotal.setVisibility(View.INVISIBLE);
                tvTransacciones.setVisibility(View.INVISIBLE);
                tvTicketMedio.setVisibility(View.INVISIBLE);
            } else {
                progressBar.setVisibility(View.GONE);
                tvTotal.setVisibility(View.VISIBLE);
                tvTransacciones.setVisibility(View.VISIBLE);
                tvTicketMedio.setVisibility(View.VISIBLE);
            }
        });

        homeViewModel.cargarDatosDelDia(db);

        cargarNombreSucursal(tvNombreSucursal);
        cargarCotizacion(tvCotizacion);

        return root;
    }

    private void cargarNombreSucursal(TextView tvNombreSucursal) {
        try {
            // Verificar si ya existe información de sucursal guardada
            if (sharedPrefs.hasSucursalInfo()) {
                SharedPreferences.SucursalInfo sucursalInfo = sharedPrefs.getSucursalInfo();
                tvNombreSucursal.setText(sucursalInfo.getNombre());
                Log.d(TAG, "Nombre de sucursal cargado: " + sucursalInfo.getNombre());
            } else {
                // Si no existe, consultar desde la API
                Log.d(TAG, "No se encontró información de sucursal, consultando desde API...");
                consultarInfoSucursalParaHome(tvNombreSucursal);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al cargar nombre de sucursal", e);
            tvNombreSucursal.setText("Sucursal");
        }
    }

    private void cargarCotizacion(TextView tvCotizacion) {
        mostrarCotizacionGuardada(tvCotizacion);
        actualizarCotizacionDesdeAPI(tvCotizacion);
    }

    private void mostrarCotizacionGuardada(TextView tvCotizacion) {
        try {
            double cotizacion = IndicesApiClient.getSavedValor1(requireContext());

            if (cotizacion > 0) {
                DecimalFormatSymbols symbols = new DecimalFormatSymbols();
                symbols.setDecimalSeparator(',');
                symbols.setGroupingSeparator('.');
                DecimalFormat formatter = new DecimalFormat("#,##0.00", symbols);
                tvCotizacion.setText("Gs. " + formatter.format(cotizacion));
                Log.d(TAG, "Cotización guardada mostrada: " + cotizacion);
            } else {
                tvCotizacion.setText("No se obtuvo la cotizacion del dia. ");
                Log.d(TAG, "No se pudo obtener la cotizacion del dia. ");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al mostrar cotización guardada", e);
            tvCotizacion.setText("No se pudo obtener la cotizacion del dia. ");
        }
    }

    private void actualizarCotizacionDesdeAPI(TextView tvCotizacion) {
        try {
            double valorGuardado = IndicesApiClient.getSavedValor1(requireContext());

            if (valorGuardado > 0) {
                DecimalFormatSymbols symbols = new DecimalFormatSymbols();
                symbols.setDecimalSeparator(',');
                symbols.setGroupingSeparator('.');
                DecimalFormat formatter = new DecimalFormat("#,##0.00", symbols);
                tvCotizacion.setText("Gs. " + formatter.format(valorGuardado));
                Log.d(TAG, "Cotización actualizada desde valor guardado: " + valorGuardado);
            } else {
                Log.d(TAG, "No hay valor guardado disponible, manteniendo cotización actual");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al obtener cotización desde valor guardado", e);
        }
    }

    private void consultarInfoSucursalParaHome(TextView tvNombreSucursal) {
        try {
            // Obtener las configuraciones sincronizadas
            java.util.List<SharedPreferences.ConfiguracionSincronizada> configuraciones = sharedPrefs.getSyncedConfiguraciones();
            
            if (configuraciones.isEmpty()) {
                Log.w(TAG, "No se encontraron configuraciones sincronizadas para consultar sucursal");
                tvNombreSucursal.setText("Sucursal");
                return;
            }

            // Usar la primera configuración disponible
            SharedPreferences.ConfiguracionSincronizada config = configuraciones.get(0);
            String codemp = config.getCodempresa();
            String codsuc = config.getCodsucursal();

            if (codemp.isEmpty() || codsuc.isEmpty()) {
                Log.w(TAG, "Códigos de empresa o sucursal vacíos en configuración");
                tvNombreSucursal.setText("Sucursal");
                return;
            }

            SucursalInfoManager sucursalInfoManager = new SucursalInfoManager(requireContext());
            sucursalInfoManager.consultarInfoSucursal(codemp, codsuc, new SucursalInfoManager.SucursalInfoCallback() {
                @Override
                public void onSuccess(SucursalInfoManager.SucursalInfo sucursalInfo) {
                    // Guardar en SharedPreferences
                    sharedPrefs.saveSucursalInfo(
                            sucursalInfo.getNombre(),
                            sucursalInfo.getDomicilio(),
                            sucursalInfo.getDomicilio2(),
                            sucursalInfo.getLocalidad(),
                            sucursalInfo.getTelefono(),
                            sucursalInfo.getCodsuc()
                    );

                    // Actualizar la UI en el hilo principal
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            tvNombreSucursal.setText(sucursalInfo.getNombre());
                            Log.d(TAG, "Información de sucursal consultada y cargada: " + sucursalInfo.getNombre());
                        });
                    }
                }

                @Override
                public void onError(String error) {
                    Log.e(TAG, "Error al consultar información de sucursal: " + error);
                    // Actualizar la UI en el hilo principal
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            tvNombreSucursal.setText("Sucursal");
                        });
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error al iniciar consulta de información de sucursal", e);
            tvNombreSucursal.setText("Sucursal");
        }
    }
}