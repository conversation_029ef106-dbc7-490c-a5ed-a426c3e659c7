package com.salesmobile;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "configuraciones")

public class Configuracion {
    @PrimaryKey
    @NonNull
    private int id;

    @ColumnInfo(name = "baseUrl")
    private String baseUrl;

    @ColumnInfo(name = "usuario")
    private String usuario;

    @ColumnInfo(name = "contrasena")
    private String contrasena;

    @ColumnInfo(name = "grantType")
    private String grantType;

    @ColumnInfo(name = "codigoEmpresa")
    private String codigoEmpresa;

    @ColumnInfo(name = "codigoSucursal")
    private String codigoSucursal;

    @ColumnInfo(name = "baseUrl2")
    private String baseUrl2;

    @ColumnInfo(name = "usuario2")
    private String usuario2;

    @ColumnInfo(name = "contrasena2")
    private String contrasena2;

    @ColumnInfo(name = "grantType2")
    private String grantType2;

    @ColumnInfo(name = "codigoEmpresa2")
    private String codigoEmpresa2;

    @ColumnInfo(name = "codigoSucursal2")
    private String codigoSucursal2;

    @ColumnInfo(name = "id_puntoventa")
    @NonNull
    private int idPuntoventa;

    // Getters y Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getContrasena() {
        return contrasena;
    }

    public void setContrasena(String contrasena) {
        this.contrasena = contrasena;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public String getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(String codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getCodigoSucursal() {
        return codigoSucursal;
    }

    public void setCodigoSucursal(String codigoSucursal) {
        this.codigoSucursal = codigoSucursal;
    }

    public String getBaseUrl2() {
        return baseUrl2;
    }

    public void setBaseUrl2(String baseUrl2) {
        this.baseUrl2 = baseUrl2;
    }

    public String getUsuario2() {
        return usuario2;
    }

    public void setUsuario2(String usuario2) {
        this.usuario2 = usuario2;
    }

    public String getContrasena2() {
        return contrasena2;
    }

    public void setContrasena2(String contrasena2) {
        this.contrasena2 = contrasena2;
    }

    public String getGrantType2() {
        return grantType2;
    }

    public void setGrantType2(String grantType2) {
        this.grantType2 = grantType2;
    }

    public String getCodigoEmpresa2() {
        return codigoEmpresa2;
    }

    public void setCodigoEmpresa2(String codigoEmpresa2) {
        this.codigoEmpresa2 = codigoEmpresa2;
    }

    public String getCodigoSucursal2() {
        return codigoSucursal2;
    }

    public void setCodigoSucursal2(String codigoSucursal2) {
        this.codigoSucursal2 = codigoSucursal2;
    }

    // Getter para idPuntoventa
    public int getIdPuntoventa() {
        return idPuntoventa;
    }

    // Setter para idPuntoventa
    public void setIdPuntoventa(int idPuntoventa) {
        this.idPuntoventa = idPuntoventa;
    }

}