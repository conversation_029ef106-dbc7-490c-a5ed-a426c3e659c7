<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/white"
    android:padding="16dp">


    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/puntoVenta"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etPuntoVenta"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Punto de Venta"/>
    </com.google.android.material.textfield.TextInputLayout>



    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/servidorLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etServidor"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Servidor SQL"/>
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/baseDatosLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etBaseDatos"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Base de Datos"/>
    </com.google.android.material.textfield.TextInputLayout>

    <Button
        android:id="@+id/btnGuardarConfig"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Guardar Configuración"/>
</LinearLayout>