<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/white"
    tools:context=".ui.client.selectClientFragment">

    <!-- Número de Documento -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/textView3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.4"
            android:text="Nro. Documento:"
            android:textColor="@color/purple_700"
            android:textSize="16sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/editNroDocumento"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.6"
            android:ems="10"
            android:inputType="text"
            android:hint="Buscar Cliente"
            android:background="@drawable/edittext_background"
            android:padding="12dp"
            android:textColor="@color/black"/>
    </LinearLayout>

    <!-- Nombre del Cliente (resultado) -->
    <TextView
        android:id="@+id/textNombre"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:padding="16dp"
        android:text="Nombre del Cliente"
        android:background="@color/teal_200"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold"
        android:visibility="visible" />

    <!-- Correo del Cliente (resultado) -->
    <TextView
        android:id="@+id/textCorreo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:padding="16dp"
        android:background="@color/teal_200"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold"
        android:text="Correo del Cliente"
        android:visibility="visible" />


    <!-- Imagen QR -->
    <ImageView
        android:id="@+id/qrImageView"
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:layout_gravity="center"
        android:layout_marginBottom="24dp"
        android:visibility="gone"
        android:contentDescription="Código QR de pago"/>


    <!-- Botón Generar QR -->
    <Button
        android:id="@+id/btnQr"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:text="Generar QR"
        android:backgroundTint="@color/purple_500"
        android:textColor="@color/white"
        android:paddingVertical="12dp"
        android:textAllCaps="false"
        android:visibility="gone"
        style="@style/Widget.MaterialComponents.Button"/>

    <!-- Botón Imprimir Factura -->
    <Button
        android:id="@+id/btnImprimirFactura"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:text="Imprimir Factura"
        android:backgroundTint="@color/purple_500"
        android:textColor="@color/white"
        android:paddingVertical="12dp"
        android:textAllCaps="false"
        android:visibility="gone"
        style="@style/Widget.MaterialComponents.Button"/>

    <!-- Botón Modificar Cliente -->
    <Button
        android:id="@+id/btnModificar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:text="Modificar Cliente"
        android:backgroundTint="@color/purple_500"
        android:textColor="@color/white"
        android:paddingVertical="12dp"
        android:textAllCaps="false"
        android:visibility="gone"
        style="@style/Widget.MaterialComponents.Button"/>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBarClienteBuscar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="24dp"
        android:indeterminateTint="@color/purple_500"
        android:visibility="gone"/>

    <!-- Botones Inferiores -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="8dp">

        <Button
            android:id="@+id/btnContinue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="Buscar"
            android:backgroundTint="@color/purple_500"
            android:textColor="@color/white"
            android:paddingVertical="12dp"
            android:textAllCaps="false"
            style="@style/Widget.MaterialComponents.Button"/>

        <Button
            android:id="@+id/btnVolver"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="Volver"
            android:backgroundTint="@color/grey"
            android:textColor="@color/white"
            android:paddingVertical="12dp"
            android:textAllCaps="false"
            style="@style/Widget.MaterialComponents.Button"/>
    </LinearLayout>

</LinearLayout>