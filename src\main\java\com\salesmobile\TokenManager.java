package com.salesmobile;

import android.content.Context;
import android.content.SharedPreferences;

import java.util.concurrent.TimeUnit;

public class TokenManager {

    private static TokenManager instance;
    private String token;
    private long tokenExpirationTime;
    private boolean isTokenRequestInProgress = false;


    private TokenManager() {
        // Constructor privado para evitar instanciación directa
    }

    public static synchronized TokenManager getInstance() {
        if (instance == null) {
            instance = new TokenManager();
        }
        return instance;
    }

    public void requestToken(Context context, Runnable onTokenReceived) {
        String existingToken = getToken(context);
        if (existingToken != null) {
            token = existingToken;
            if (onTokenReceived != null) {
                onTokenReceived.run();
            }
            return;
        }

        if (isTokenRequestInProgress) {
            return;
        }

        isTokenRequestInProgress = true;

        TokenRequest tokenRequest = new TokenRequest(context, new TokenRequest.TokenRequestListener() {
            @Override
            public void onTokenReceived(String receivedToken) {
                token = receivedToken;
                tokenExpirationTime = System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(30);
                isTokenRequestInProgress = false;

                saveToken(context, token, tokenExpirationTime);

                if (onTokenReceived != null) {
                    onTokenReceived.run();
                }
            }
        });
        tokenRequest.execute();
    }

    private void saveToken(Context context, String token, long expirationTime) {
        SharedPreferences sharedPreferences = context.getSharedPreferences("AppPrefs", Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString("token", token);
        editor.putLong("tokenExpirationTime", expirationTime);
        editor.apply();
    }

    private String getToken(Context context) {
        SharedPreferences sharedPreferences = context.getSharedPreferences("AppPrefs", Context.MODE_PRIVATE);
        long expirationTime = sharedPreferences.getLong("tokenExpirationTime", 0);

        if (System.currentTimeMillis() < expirationTime) {
            return sharedPreferences.getString("token", null);
        } else {
            return null; // El token ha expirado
        }
    }

    public String getToken() {
        return token;
    }
}