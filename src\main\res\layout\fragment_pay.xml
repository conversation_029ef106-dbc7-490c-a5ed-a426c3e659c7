<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".ui.pay.payFragment">

    <!-- <PERSON><PERSON><PERSON> de éxito con mejor estilo -->

    <!-- Contenedor para los botones con alineación consistente -->
    <TextView
        android:id="@+id/textTitulo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="50dp"
        android:layout_marginEnd="24dp"
        android:gravity="center"
        android:lineSpacingExtra="8sp"
        android:text="Gracias por su compra!"
        android:textSize="24sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/textNroFactura"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="100dp"
        android:layout_marginEnd="18dp"
        android:gravity="center"
        android:text="Nro de Factura"
        android:textColor="@color/purple_700"
        android:textSize="24sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="280dp"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        android:padding="16dp">

        <!-- Botón para imprimir factura -->
        <Button
            android:id="@+id/btnImprimirFactura"
            android:layout_width="240dp"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:text="Imprimir Factura"
            android:textColor="@color/white"
            android:textAllCaps="false"
            android:textSize="16sp"
             />

        <!-- Botón para imprimir recibo -->
        <Button
            android:id="@+id/btnImprimirRecibo"
            android:layout_width="240dp"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:text="Imprimir Recibo"
            android:textColor="@color/white"
            android:textAllCaps="false"
            android:textSize="16sp"
             />

        <!-- Botón primario con estilo -->
        <Button
            android:id="@+id/otraVenta"
            android:layout_width="240dp"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:text="Cargar otra Venta"
            android:textColor="@color/white"
            android:textAllCaps="false"
            android:textSize="16sp" />

        <!-- Botón secundario con estilo -->
        <Button
            android:id="@+id/home"
            android:layout_width="240dp"
            android:layout_height="48dp"

            android:text="Ir al Inicio"

            android:textAllCaps="false"
            android:textSize="16sp" />
    </LinearLayout>

    <!-- Progress Bar para operaciones de impresión -->
    <ProgressBar
        android:id="@+id/progressBarImprimir"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="200dp"
        android:indeterminateTint="@color/purple_500"
        android:visibility="gone" />

</FrameLayout>