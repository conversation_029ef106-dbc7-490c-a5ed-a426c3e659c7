package com.salesmobile;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.salesmobile.config.SharedPreferences;

public class ParametrosTabFragment extends Fragment {

    private TextView tvTest, tvTestPc, tvEntorno, tvDevBancardUrl;
    private TextView tvProdBancardUrl, tvDevDefaultUrl, tvProdDefaultUrl;
    private TextView tvBancardCommerceCode, tvBancardCommerceBranch, tvAppmovilDefaultUrl;
    private SharedPreferences sharedPreferences;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.tab_parametros, container, false);

        sharedPreferences = new SharedPreferences(getContext());

        initializeViews(view);
        loadParametros();

        return view;
    }

    private void initializeViews(View view) {
        tvTest = view.findViewById(R.id.tvTest);
        tvTestPc = view.findViewById(R.id.tvTestPc);
        tvEntorno = view.findViewById(R.id.tvEntorno);
        tvDevBancardUrl = view.findViewById(R.id.tvDevBancardUrl);
        tvProdBancardUrl = view.findViewById(R.id.tvProdBancardUrl);
        tvDevDefaultUrl = view.findViewById(R.id.tvDevDefaultUrl);
        tvProdDefaultUrl = view.findViewById(R.id.tvProdDefaultUrl);
        tvBancardCommerceCode = view.findViewById(R.id.tvBancardCommerceCode);
        tvBancardCommerceBranch = view.findViewById(R.id.tvBancardCommerceBranch);
        tvAppmovilDefaultUrl = view.findViewById(R.id.tvAppmovilDefaultUrl);
    }

    private void loadParametros() {
        SharedPreferences.ParametrosInfo parametros = sharedPreferences.getParametros();

        if (parametros != null) {
            tvTest.setText(getValueOrDefault(parametros.getTest()));
            tvTestPc.setText(getValueOrDefault(parametros.getTestPc()));
            tvEntorno.setText(getValueOrDefault(parametros.getEntorno()));
            tvDevBancardUrl.setText(getValueOrDefault(parametros.getDevBancardUrl()));
            tvProdBancardUrl.setText(getValueOrDefault(parametros.getProdBancardUrl()));
            tvDevDefaultUrl.setText(getValueOrDefault(parametros.getDevDefaultUrl()));
            tvProdDefaultUrl.setText(getValueOrDefault(parametros.getProdDefaultUrl()));
            tvBancardCommerceCode.setText(String.valueOf(parametros.getBancardCommerceCode()));
            tvBancardCommerceBranch.setText(String.valueOf(parametros.getBancardCommerceBranch()));
            tvAppmovilDefaultUrl.setText(getValueOrDefault(parametros.getAppmovilDefaultUrl()));
        } else {
            tvTest.setText("No hay datos disponibles");
            tvTestPc.setText("No hay datos disponibles");
            tvEntorno.setText("No hay datos disponibles");
            tvDevBancardUrl.setText("No hay datos disponibles");
            tvProdBancardUrl.setText("No hay datos disponibles");
            tvDevDefaultUrl.setText("No hay datos disponibles");
            tvProdDefaultUrl.setText("No hay datos disponibles");
            tvBancardCommerceCode.setText("0");
            tvBancardCommerceBranch.setText("0");
            tvAppmovilDefaultUrl.setText("No hay datos disponibles");
        }
    }

    private String getValueOrDefault(String value) {
        return (value != null && !value.trim().isEmpty()) ? value : "No hay datos disponibles";
    }
}
