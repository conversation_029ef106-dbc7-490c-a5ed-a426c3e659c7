package com.salesmobile.ui.client;

import com.android.volley.toolbox.JsonObjectRequest;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.os.Bundle;
import androidx.fragment.app.Fragment;

import android.os.Looper;
import android.util.Base64;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.android.volley.AuthFailureError;
import com.android.volley.DefaultRetryPolicy;
import com.android.volley.RequestQueue;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.android.volley.TimeoutError;
import com.android.volley.VolleyError;
import com.google.gson.Gson;
import com.google.zxing.BarcodeFormat;
import com.salesmobile.AppDatabase;
import com.salesmobile.Configuracion;
import com.salesmobile.R;
import com.salesmobile.SucursalInfoManager;
import com.salesmobile.TokenRequest;
import com.salesmobile.config.ParametrosConf;

import com.salesmobile.ui.factura.FacturaManager;
import com.salesmobile.ui.factura.Producto;
import com.salesmobile.ui.inputproduct.Product;
import com.salesmobile.ui.inputproduct.SharedViewModel;
import com.salesmobile.utils.enviarEmail;
import com.salesmobile.utils.generadorFacturas;
import com.salesmobile.utils.generadorRecibos;
import com.salesmobile.utils.impresion;

import android.app.AlertDialog;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import org.apache.http.conn.ConnectTimeoutException;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONArray;

import java.io.EOFException;
import java.io.File;
import java.io.IOException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import okhttp3.ConnectionPool;
import retrofit2.Response;

import android.os.Handler;

import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import retrofit2.Callback;

import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;

import com.journeyapps.barcodescanner.BarcodeEncoder;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;

import com.google.gson.annotations.SerializedName;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLHandshakeException;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.net.ssl.SSLSocketFactory;



public class selectClientFragment extends Fragment implements TokenRequest.TokenRequestListener {
    private EditText editNroDocumento;
    private TextView textNombre;
    private TextView textCorreo;
    private Button btnQr;
    private Button btnModificar;
    private String token;
    private String baseUrl;
    private String CodEmpresa;
    private String CodSucursal;
    private String codigoCliente;
    private ProgressBar progressBarClienteBuscar;
    private ProgressBar progressBarDialogo;
    private SharedViewModel viewModel;
    // Métodos para obtener valores dinámicamente según el entorno
    private String getBancardBaseUrl() {
        return ParametrosConf.BANCARD_BASE_URL;
    }

    private String getBancardPublicKey() {
        return ParametrosConf.BANCARD_PUBLIC_KEY;
    }

    private String getBancardPrivateKey() {
        return ParametrosConf.BANCARD_PRIVATE_KEY;
    }

    private String getCommerceCode() {
        return ParametrosConf.COMMERCE_CODE;
    }

    private String getCommerceBranch() {
        return ParametrosConf.COMMERCE_BRANCH;
    }
    
    private static final String BASE_URL = ParametrosConf.BASE_URL;
    private static final String API2_URL = ParametrosConf.API2_URL;
    private static final String API1_URL = ParametrosConf.API1_URL;

    private AlertDialog qrDialog;
    private ImageView qrImageView;

    private BancardApiService bancardApiService;
    private Handler paymentHandler = new Handler(Looper.getMainLooper());
    private Runnable paymentCheckRunnable;
    private String currentHookAlias; // Para almacenar el ID del QR actual

    private static final int TIEMPO_ESPERA_CALLBACK = 120000; // 2 minuto (en milisegundos)
    private long tiempoInicioTransaccion;
    private static final String BANCARD_VERIFICATION_URL = ParametrosConf.BASE_URL + "consultarEstado.php";
   
    private List<Producto> productosQR; // Añade esta línea

    private boolean shouldRevertPayment = true; // Controla si se debe revertir
    private String lastPaymentErrorCode = ""; // Guarda el último código de error
    private boolean alreadyRetried = false; // Controla si ya se hizo un reintento
    private boolean paymentVerified = false; // Indica si se recibió alguna respuesta
    private boolean receivedApiResponse = false;
    private boolean shouldAutoRevert = true;
    private Handler timeoutHandler = new Handler(Looper.getMainLooper());
    private Runnable timeoutRunnable;
    private boolean responseReceived = false;
    private boolean isVerificationActive = false;
    private boolean shouldContinueVerification = true;
    private Handler verificationHandler = new Handler(Looper.getMainLooper());
    private Runnable verificationRunnable;

    private String merchant_code;
    private String ticket_number;
    private String card_last_numbers;

    private boolean reciboGenerado = false;
    private String reciboPdfClientePath;
    private String reciboPdfComercioPath;
    private File pdfGuardado;

    private void obtenerBaseUrl() {
        Executors.newSingleThreadExecutor().execute(() -> {
            AppDatabase db = AppDatabase.getInstance(requireContext());
            Configuracion configuracion = db.configuracionDao().obtenerConfiguracion();
            String empresa = configuracion != null ? configuracion.getCodigoEmpresa() : "1";
            String sucursal = configuracion != null ? configuracion.getCodigoSucursal() : "1";

            // Actualizar la UI en el hilo principal
            requireActivity().runOnUiThread(() -> {
                baseUrl = API1_URL;
                CodEmpresa = empresa;
                CodSucursal = sucursal;
            });
        });
    }

    private void consultarInfoSucursalSiEsNecesario() {
        com.salesmobile.config.SharedPreferences sharedPrefs = new com.salesmobile.config.SharedPreferences(requireContext());

        // Verificar si ya se consultó la información de sucursal
        if (sharedPrefs.hasSucursalInfo()) {
            Log.d("SelectClient", "Información de sucursal ya existe, no es necesario consultarla");
            return;
        }

        Log.d("SelectClient", "Consultando información de sucursal por primera vez...");

        // Obtener los parámetros necesarios desde las configuraciones sincronizadas
        java.util.List<com.salesmobile.config.SharedPreferences.ConfiguracionSincronizada> configuraciones = sharedPrefs.getSyncedConfiguraciones();

        if (configuraciones.isEmpty()) {
            Log.w("SelectClient", "No se encontraron configuraciones sincronizadas para consultar sucursal");
            return;
        }

        // Usar la primera configuración disponible
        com.salesmobile.config.SharedPreferences.ConfiguracionSincronizada config = configuraciones.get(0);
        String codemp = config.getCodempresa();
        String codsuc = config.getCodsucursal();

        if (codemp == null || codemp.isEmpty() || codsuc == null || codsuc.isEmpty()) {
            Log.w("SelectClient", "No se encontraron códigos de empresa o sucursal en la configuración");
            return;
        }

        SucursalInfoManager sucursalInfoManager = new SucursalInfoManager(requireContext());
        sucursalInfoManager.consultarInfoSucursal(codemp, codsuc, new SucursalInfoManager.SucursalInfoCallback() {
            @Override
            public void onSuccess(SucursalInfoManager.SucursalInfo sucursalInfo) {
                // Guardar en SharedPreferences
                sharedPrefs.saveSucursalInfo(
                        sucursalInfo.getNombre(),
                        sucursalInfo.getDomicilio(),
                        sucursalInfo.getDomicilio2(),
                        sucursalInfo.getLocalidad(),
                        sucursalInfo.getTelefono(),
                        sucursalInfo.getCodsuc()
                );
                Log.d("SelectClient", "Información de la sucursal cargada y guardada correctamente en primera navegación.");
            }

            @Override
            public void onError(String errorMessage) {
                Log.e("SelectClient", "Error al consultar información de sucursal en primera navegación: " + errorMessage);
            }
        });
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Usar URL correcta según el entorno
        String baseUrlToUse = getBancardBaseUrl();

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(15, TimeUnit.SECONDS)
                .readTimeout(15, TimeUnit.SECONDS)
                .writeTimeout(15, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .addInterceptor(new RetryInterceptor())
                // .addInterceptor(new HttpLoggingInterceptor().setLevel(HttpLoggingInterceptor.Level.BODY))
                .addInterceptor(new Interceptor() {
                    @NotNull
                    @Override
                    public okhttp3.Response intercept(@NotNull Interceptor.Chain chain) throws IOException {
                        okhttp3.Request request = chain.request()
                                .newBuilder()
                                .header("Connection", "keep-alive")
                                .header("Keep-Alive", "timeout=90, max=1000")
                                .build();
                        return chain.proceed(request);
                    }
                })
                .connectionPool(new ConnectionPool(5, 5, TimeUnit.MINUTES))
                .sslSocketFactory(getSSLSocketFactory(), getTrustManager())
                .hostnameVerifier((hostname, session) -> true)
                .build();

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(baseUrlToUse)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        bancardApiService = retrofit.create(BancardApiService.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_select_client, container, false);
        Log.d("TAG", "Este es un mensaje de registro de depuración");

        viewModel = new ViewModelProvider(requireActivity()).get(SharedViewModel.class);

        progressBarClienteBuscar = view.findViewById(R.id.progressBarClienteBuscar);


        obtenerBaseUrl();

        // Obtener el argumento del nombre del cliente
        String nombreClient = getArguments() != null ? getArguments().getString("nombreCliente") : null;
        String numeroDocumento = getArguments() != null ? getArguments().getString("nroDocumento") : null;
        codigoCliente = getArguments() != null ? getArguments().getString("codigoCliente") : null;
        String email = getArguments() != null ? getArguments().getString("email") : null;

        // Inicializa el ImageView para el QR
        qrImageView = view.findViewById(R.id.qrImageView);

        btnQr = view.findViewById(R.id.btnQr);
        btnModificar = view.findViewById(R.id.btnModificar);

        btnQr.setVisibility(View.GONE);
        btnModificar.setVisibility(View.GONE);

        // Obtener los argumentos enviados desde ClientFragment si se guardo bien el cliente muestro el boton de pago QR
        Bundle args = getArguments();
        if (args != null) {
            boolean clienteGuardado = args.getBoolean("clienteGuardado", false);
            if (clienteGuardado) {
                btnQr.setVisibility(View.VISIBLE);
                btnModificar.setVisibility(View.VISIBLE);
            }
        }

        textNombre = view.findViewById(R.id.textNombre);
        textCorreo = view.findViewById(R.id.textCorreo);
        editNroDocumento = view.findViewById(R.id.editNroDocumento);

        editNroDocumento.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE) {
                consultarCliente(editNroDocumento.getText().toString(), null);
                return true;
            }
            return false;
        });

        if (nombreClient != null) {
            textNombre.setText(nombreClient);
            textCorreo = view.findViewById(R.id.textCorreo);
            editNroDocumento.setText(numeroDocumento);
            Log.d("TAG", "Cliente recibido: " + nombreClient);
        }

        if (email != null) {
            textCorreo.setText(email);
            textCorreo = view.findViewById(R.id.textCorreo);
            Log.d("TAG", "Correo recibido: " + email);
        }


        obtenerToken(); // Obtener el token antes de hacer consultas
        consultarInfoSucursalSiEsNecesario(); // Consultar información de sucursal solo la primera vez

        Button btnVolver = view.findViewById(R.id.btnVolver);
        btnVolver.setOnClickListener(v -> {
            NavController navController = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
            navController.navigate(R.id.nav_carritoFragment);
        });

        Button btnContinuar = view.findViewById(R.id.btnContinue);
        btnContinuar.setOnClickListener(v -> {
            String nroDocumento = editNroDocumento.getText().toString().trim();
            Log.d("DEBUG", "Botón Continuar presionado. Documento ingresado: " + nroDocumento);

            if (!nroDocumento.isEmpty()) {
                consultarCliente(nroDocumento, () -> {
                    Log.d("DEBUG", "Cliente consultado correctamente. Navegando a Fragment_pay.");
                    NavController navController = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
                    navController.navigate(R.id.action_to_Fragment_pay);
                });
            } else {
                Log.d("DEBUG", "Número de documento vacío, no se consulta el cliente.");
            }
        });


        btnQr.setOnClickListener(v -> {
            String clienteCodigo = editNroDocumento.getText().toString().trim();
            if (clienteCodigo.isEmpty()) {
                Toast.makeText(getContext(), "Debe seleccionar un cliente primero", Toast.LENGTH_SHORT).show();
                return;
            }

            List<Producto> productos = obtenerProductosDelCarrito();
            if (productos.isEmpty()) {
                Toast.makeText(getContext(), "No hay productos en el carrito", Toast.LENGTH_SHORT).show();
                return;
            }

            progressBarClienteBuscar.setVisibility(View.VISIBLE);

            // Calcular el monto total del carrito
            double totalAmount = calcularTotalCarrito(productos);

            // Generar el QR de pago
            generarQRDePago(totalAmount, productos);
        });


        btnModificar.setOnClickListener(v -> {
            String nroDocumento = editNroDocumento.getText().toString().trim();
            mostrarDialogoModificacionCliente(nroDocumento);
        });

        return view;
    }

    private class RetryInterceptor implements Interceptor {
        @Override
        public okhttp3.Response intercept(Chain chain) throws IOException {
            okhttp3.Request request = chain.request();
            okhttp3.Response response = null;
            IOException exception = null;

            try {
                response = chain.proceed(request);

                // Si la respuesta es exitosa, retornarla inmediatamente
                if (response.isSuccessful()) {
                    return response;
                }

                // Solo reintentar para ciertos códigos de error
                if (shouldRetry(response.code())) {
                    throw new IOException("Server error: " + response.code());
                }
                return response;

            } catch (IOException e) {
                // Solo reintentar para errores de conexión específicos
                if (isRetryable(e)) {
                    throw e; // Esto activará el reintento
                }
                throw e; // No reintentar para otros errores
            }
        }

        private boolean shouldRetry(int statusCode) {
            return statusCode == 408 || statusCode == 429 ||
                    statusCode >= 500 && statusCode <= 504;
        }

        private boolean isRetryable(IOException e) {
            return e instanceof SocketTimeoutException ||
                    e instanceof ConnectTimeoutException ||
                    e instanceof SSLHandshakeException ||
                    e instanceof SocketException;
        }
    }

    private double calcularTotalCarrito(List<Producto> productos) {
        double total = 0;
        double precio = 0;
        double cantidad = 0;
        for (Producto producto : productos) {
            precio = Double.parseDouble(producto.getPrecio()) ;
            cantidad = Double.parseDouble(producto.getCantidad());
            total += precio * cantidad;
        }
        return total;
    }

    private boolean seExcedioTiempoEspera() {
        long tiempoTranscurrido = System.currentTimeMillis() - tiempoInicioTransaccion;
        return tiempoTranscurrido > TIEMPO_ESPERA_CALLBACK;
    }

//#region generarQRDePago
    private void generarQRDePago(double amount, List<Producto> productos) {
        shouldRevertPayment = true;
        lastPaymentErrorCode = "";
        tiempoInicioTransaccion = System.currentTimeMillis();
        QRRequest qrRequest = new QRRequest();
        qrRequest.setAmount(amount);
        qrRequest.setDescription("Compra en Tienda de Nike");

        this.productosQR = productos;

        progressBarClienteBuscar.setVisibility(View.VISIBLE);

        QRCallback callback = new QRCallback(productos); // solo este

        Call<QRResponse> call = bancardApiService.generateQR(
                getCommerceCode(),
                getCommerceBranch(),
                qrRequest,
                getBancardAuthHeader()
        );

        Log.d("QR_RESPONSE", "Llamando a enqueue con callback");
        call.enqueue(callback); // 🔧 acá usás el mismo callback
    }
//#endregion



    // Clase interna para manejar el callback del QR
    private class QRCallback implements Callback<QRResponse> {
        private final List<Producto> productos;

        public QRCallback(List<Producto> productos) {
            this.productos = productos;
        }

        @Override
        public void onResponse(Call<QRResponse> call, Response<QRResponse> response) {
            hideProgressBar();

            Log.d("QR_RESPONSE", "Llamada a onResponse");
            Log.d("QR_RESPONSE", "Response code: " + response.code());
            Log.d("QR_RESPONSE", "Response message: " + response.message());
            Log.d("QR_RESPONSE", "Response successful: " + response.isSuccessful());
            Log.d("QR_RESPONSE", "Response headers: " + response.headers().toString());

            if (response.body() == null) {
                Log.e("QR_RESPONSE", "Cuerpo de respuesta es null");
                Log.e("QR_RESPONSE", "Error body: " + (response.errorBody() != null ?
                    "Presente (tamaño: " + response.errorBody().contentLength() + ")" : "null"));

                // Intentar leer el error body para más información
                if (response.errorBody() != null) {
                    try {
                        String errorString = response.errorBody().string();
                        Log.e("QR_RESPONSE", "Error body content: " + errorString);
                    } catch (Exception e) {
                        Log.e("QR_RESPONSE", "No se pudo leer error body", e);
                    }
                }
                return;
            }

            QRResponse qrResponse = response.body();
            Log.d("QR_RESPONSE", "Respuesta JSON: " + new Gson().toJson(qrResponse));
            Log.d("QR_RESPONSE", "Status: " + qrResponse.getStatus());

            if ("success".equalsIgnoreCase(qrResponse.getStatus()) && qrResponse.getQrExpress() != null) {
                currentHookAlias = qrResponse.getQrExpress().getHookAlias();
                String hookAlias = currentHookAlias;
                double monto = Double.parseDouble(qrResponse.getQrExpress().getAmount());
                String descripcion = qrResponse.getQrExpress().getDescription();
                String fechaCreacion = qrResponse.getQrExpress().getCreatedAt();

                registrarHookAlias(hookAlias, monto, descripcion, fechaCreacion);


                mostrarQRDialog(
                        qrResponse.getQrExpress().getUrl(),
                        qrResponse.getQrExpress().getQrData()
                );

            } else {
                Log.e("QR_RESPONSE", "Status no es success o qr_express es null");
            }
        }


        @Override
        public void onFailure(Call<QRResponse> call, Throwable t) {
            Log.e("QR_RESPONSE", "FALLÓ LA LLAMADA A LA API", t);

            int maxRetries = 3;
            int currentRetryCount = call.request().tag(Integer.class) != null
                    ? call.request().tag(Integer.class) : 0;

            if (t instanceof EOFException && currentRetryCount < maxRetries) {
                Log.w("QR_RESPONSE", "Retrying tras EOFException... (Intento " + (currentRetryCount + 1) + ")");

                Call<QRResponse> clonedCall = call.clone();
                clonedCall.request().newBuilder().tag(Integer.class, currentRetryCount + 1);
                clonedCall.enqueue(this);

                // No ocultar el progressBar todavía, porque va a reintentar
                return;
            }

            // Solo ocultar si no hay más reintentos
            progressBarClienteBuscar.setVisibility(View.GONE);
            Toast.makeText(requireContext(), "Error al generar QR", Toast.LENGTH_SHORT).show();
        }


    }

    private void reintentarGenerarQR(List<Producto> productos) {
        progressBarClienteBuscar.setVisibility(View.VISIBLE);
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            generarQRDePago(calcularTotalCarrito(productos), productos);
        }, 3000); // Reintentar después de 3 segundos
    }


    private String getBancardAuthHeader() {
        String publicKey = getBancardPublicKey();
        String privateKey = getBancardPrivateKey();
        String credentials = publicKey + ":" + privateKey;
        String base64Credentials = Base64.encodeToString(credentials.getBytes(), Base64.NO_WRAP);

        // Logging detallado para diagnosticar el problema
        Log.d("BANCARD_AUTH", "=== DIAGNÓSTICO DETALLADO ===");
        Log.d("BANCARD_AUTH", "ENV_STAGE: '" + ParametrosConf.ENV_STAGE + "'");
        Log.d("BANCARD_AUTH", "ENV_STAGE.trim(): '" + (ParametrosConf.ENV_STAGE != null ? ParametrosConf.ENV_STAGE.trim() : "null") + "'");

        // Obtener valores directamente de SharedPreferences para comparar
        com.salesmobile.config.SharedPreferences sharedPrefs = new com.salesmobile.config.SharedPreferences(requireContext());
        com.salesmobile.config.SharedPreferences.ParametrosInfo parametros = sharedPrefs.getParametros();

        // Refrescar ParametrosConf para aplicar la nueva lógica con trim()
        ParametrosConf.refresh();
        return "Basic " + base64Credentials;
    }

//#region mostrarQRDialog
    private void mostrarQRDialog(String qrImageUrl, String qrData) {

        requireActivity().runOnUiThread(() -> {
            try {
                requireActivity().getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

                AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());

                View dialogView = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_qr, null);

                ImageView dialogQrImageView = dialogView.findViewById(R.id.qrImageView);
                TextView textHookAlias = dialogView.findViewById(R.id.textHookAlias);
                Button btnClose = dialogView.findViewById(R.id.btnClose);
                progressBarDialogo = dialogView.findViewById(R.id.progressBar);

                // Cargar imagen QR desde URL usando Glide con caché
                cargarImagenQRDesdeURL(qrImageUrl, qrData, dialogQrImageView);

                // Mostrar el hook_alias
                if (currentHookAlias != null) {
                    textHookAlias.setText(currentHookAlias);
                    textHookAlias.setVisibility(View.VISIBLE);
                }

                builder.setView(dialogView);
                qrDialog = builder.create();

                // evitar que se toque fuera del dialogo
                qrDialog.setCanceledOnTouchOutside(false);
                btnQr.setEnabled(false);
                btnModificar.setEnabled(false);
                qrDialog.setCancelable(false);

                btnClose.setOnClickListener(v -> {
                    revertirPagoSiEsNecesario();
                    qrDialog.dismiss();
                });

                qrDialog.setOnDismissListener(dialog -> {
                    requireActivity().getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                    btnQr.setEnabled(true);
                    btnModificar.setEnabled(true);
                    // Detener todas las verificaciones
                    shouldContinueVerification = false;
                    if (verificationRunnable != null) {
                        verificationHandler.removeCallbacks(verificationRunnable);
                    }

                    // Solo revertir si no se recibió una respuesta definitiva
                    if (!paymentVerified) {
                        revertirPagoSiEsNecesario();
                    }
                });

                qrDialog.show();
                progressBarDialogo.setVisibility(View.GONE);

                // Iniciar verificación de pago
                iniciarVerificacionPago(productosQR);

            } catch (Exception e) {
                Log.e("DIALOG_ERROR", "Error mostrando diálogo QR", e);
            }
        });
    }
//#endregion

//#region iniciarVerificacionPago
    private void iniciarVerificacionPago(List<Producto> productos) {
        // Cancelar cualquier verificación previa
        if (verificationRunnable != null) {
            verificationHandler.removeCallbacks(verificationRunnable);
        }

        // Resetear bandera de continuación
        shouldContinueVerification = true;

        verificationRunnable = new Runnable() {
            @Override
            public void run() {
                if (shouldContinueVerification && !seExcedioTiempoEspera()) {
                    verificarEstadoPago(productos);
                    verificationHandler.postDelayed(this, 5000); // Verificar cada 5 segundos
                } else if (seExcedioTiempoEspera()) {
                    // Solo revertir si se excedió el tiempo y no hubo respuesta definitiva
                    if (shouldContinueVerification) {
                        revertirPagoSiEsNecesario();
                        requireActivity().runOnUiThread(() -> {
                            if (qrDialog != null && qrDialog.isShowing()) {
                                qrDialog.dismiss();
                            }

                            new AlertDialog.Builder(requireContext())
                                    .setTitle("Pago no procesado")
                                    .setMessage("No hubo respuesta de Bancard")
                                    .setPositiveButton("Aceptar", null)
                                    .setCancelable(false)
                                    .show();
                        });
                    }
                }
            }
        };

        // Iniciar inmediatamente y luego cada 5 segundos
        verificationHandler.post(verificationRunnable);
    }
//#endregion

    private void cargarImagenQRDesdeURL(String qrImageUrl, String qrData, ImageView imageView) {
        if (qrImageUrl == null || qrImageUrl.isEmpty()) {
            Log.w("QR_DEBUG", "URL de imagen QR vacía, usando generación local como fallback");
            generarQRDesdeDatos(qrData, imageView);
            return;
        }

        Log.d("QR_DEBUG", "Cargando imagen QR desde URL: " + qrImageUrl);

        // Configurar opciones de Glide para caché
        RequestOptions requestOptions = new RequestOptions()
                .diskCacheStrategy(DiskCacheStrategy.ALL) // Cachear tanto la imagen original como las transformadas
                .placeholder(R.drawable.ic_launcher_background) // Placeholder mientras carga
                .error(R.drawable.ic_launcher_background); // Imagen de error

        Glide.with(requireContext())
                .load(qrImageUrl)
                .apply(requestOptions)
                .into(new com.bumptech.glide.request.target.CustomTarget<android.graphics.drawable.Drawable>() {
                    @Override
                    public void onResourceReady(android.graphics.drawable.Drawable resource,
                                              com.bumptech.glide.request.transition.Transition<? super android.graphics.drawable.Drawable> transition) {
                        hideProgressBar();
                        imageView.setImageDrawable(resource);
                        Log.d("QR_DEBUG", "Imagen QR cargada exitosamente desde URL y cacheada");
                    }

                    @Override
                    public void onLoadCleared(android.graphics.drawable.Drawable placeholder) {
                        // Método requerido por CustomTarget
                    }

                    @Override
                    public void onLoadFailed(android.graphics.drawable.Drawable errorDrawable) {
                        Log.w("QR_DEBUG", "Error cargando imagen QR desde URL, usando generación local como fallback");
                        // Fallback a generación local si falla la carga desde URL
                        generarQRDesdeDatos(qrData, imageView);
                    }
                });
    }

    private void generarQRDesdeDatos(String qrData, ImageView imageView) {
        if (qrData == null || qrData.isEmpty()) {
            hideProgressBar();
            Log.e("QR_ERROR", "Datos QR vacíos o nulos");
            return;
        }

        try {
            BarcodeEncoder barcodeEncoder = new BarcodeEncoder();
            Bitmap bitmap = barcodeEncoder.encodeBitmap(
                    qrData,
                    BarcodeFormat.QR_CODE,
                    600,  // Mayor tamaño para mejor calidad
                    600
            );

            if (bitmap != null) {
                hideProgressBar();
                imageView.setImageBitmap(bitmap);
                Log.d("QR_DEBUG", "QR generado exitosamente desde datos locales");
            } else {
                Log.e("QR_ERROR", "Bitmap generado es nulo");
                Toast.makeText(requireContext(), "Error al generar QR", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            hideProgressBar();
            Log.e("QR_ERROR", "Error generando QR: " + e.getMessage());
            Toast.makeText(requireContext(), "Error al generar QR: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        progressBarClienteBuscar.setVisibility(View.GONE);

        if (timeoutHandler != null && timeoutRunnable != null) {
            timeoutHandler.removeCallbacks(timeoutRunnable);
        }

        if (qrDialog != null && qrDialog.isShowing()) {

            qrDialog.dismiss();

        }
        qrDialog = null; //
        // Cancelar verificación de pago
        if (paymentHandler != null && paymentCheckRunnable != null) {
            paymentHandler.removeCallbacks(paymentCheckRunnable);
        }
        // Cerrar diálogo si está abierto
        if (qrDialog != null && qrDialog.isShowing()) {
            qrDialog.dismiss();
        }
    }

//#region verificarEstadoPago
    private void verificarEstadoPago(List<Producto> productos) {
        if (currentHookAlias == null || isVerificationActive) {
            return;
        }

        isVerificationActive = true;
        Log.d("PAYMENT_VERIFICATION", "Iniciando verificación para hookAlias: " + currentHookAlias);

        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("hookalias", currentHookAlias);
        } catch (JSONException e) {
            Log.e("JSON_ERROR", "Error creando JSON", e);
            isVerificationActive = false;
            return;
        }

        RequestQueue queue = Volley.newRequestQueue(requireContext());
        JsonObjectRequest jsonRequest = new JsonObjectRequest(
                com.android.volley.Request.Method.POST,
                BANCARD_VERIFICATION_URL,
                requestBody,
                response -> {
                    isVerificationActive = false;
                    Log.d("PAYMENT_RESPONSE", "Respuesta recibida: " + response.toString());

                    try {
                        if (response.has("status") && response.getString("status").equals("success")) {
                            JSONArray dataArray = response.getJSONArray("data");
                            if (dataArray.length() > 0) {
                                JSONObject paymentData = dataArray.getJSONObject(0);

                                // Verificar si es una respuesta de error temporal
                                if (paymentData.has("resultado") && paymentData.getInt("resultado") == -1) {
                                    Log.d("PAYMENT_INFO", "Transacción aún no disponible, reintentando...");
                                    return;
                                }

                                if (paymentData.has("status")) {
                                    String paymentStatus = paymentData.getString("status");

                                    if ("confirmed".equals(paymentStatus)) {
                                        shouldContinueVerification = false;
                                        ticket_number = paymentData.getString("authorization_code");
                                        merchant_code = paymentData.getString("merchant_code");
                                        card_last_numbers = paymentData.getString("card_last_numbers");
                                        handlePaymentSuccess(productos,ticket_number,merchant_code,card_last_numbers);
                                    } else if ("failed".equals(paymentStatus)) {
                                        shouldContinueVerification = false;
                                        handlePaymentFailure(paymentData.optString("response_description", "Pago fallido"));
                                    }
                                }
                            }
                        }
                    } catch (JSONException e) {
                        Log.e("JSON_ERROR", "Error parsing response", e);
                    }
                },
                error -> {
                    isVerificationActive = false;
                    Log.e("PAYMENT_ERROR", "Error en la solicitud: " + error.toString());
                }
        );

        queue.add(jsonRequest);
    }

//#endregion


//#region handlePaymentStatus
    private void handlePaymentStatus(String status, JSONObject paymentData) {
        try {
            String errorDesc = paymentData.optString("response_description", "");

            requireActivity().runOnUiThread(() -> {
                if (qrDialog != null && qrDialog.isShowing()) {
                    qrDialog.dismiss();
                }

                // Mostrar mensaje adecuado según el tipo de error
                if ("failed".equals(status)) {
                    new AlertDialog.Builder(requireContext())
                            .setTitle("Pago no procesado")
                            .setMessage("Estado: " + status + "\nRazón: " + errorDesc)
                            .setPositiveButton("Aceptar", null)
                            .show();
                }
            });

            // Marcamos que NO se debe revertir (para el onDismiss del diálogo)
            shouldRevertPayment = false;
            lastPaymentErrorCode = paymentData.optString("response_code", "");

        } catch (Exception e) {
            Log.e("PAYMENT_ERROR", "Error manejando estado", e);
        }
    }
//#endregion

//#region handlePaymentSuccess
    private void handlePaymentSuccess(List<Producto> productos, String ticket_number, String merchant_code, String card_last_numbers ) {
        paymentVerified = true;
        verificationHandler.removeCallbacks(verificationRunnable);
        mostrarSpinnerProcesandoPago(); 
        if (qrDialog != null && qrDialog.isShowing()) {
            qrDialog.dismiss();
        }

        try {
            JSONObject reciboInfo = new JSONObject();
            // Crear objeto paymentData con la información disponible
            JSONObject paymentData = new JSONObject();
            paymentData.put("ticket_number", ticket_number);
            paymentData.put("merchant_code", merchant_code);
            paymentData.put("card_last_numbers", card_last_numbers);

            reciboInfo.put("paymentData", paymentData);
            reciboInfo.put("productos", new JSONArray(productos.toString()));
            reciboInfo.put("generated", false);
            viewModel.setReciboData(reciboInfo);
        } catch (JSONException e) {
            Log.e("PAYMENT_SUCCESS", "Error creando reciboInfo", e);
        }

        grabarFactura(productos,ticket_number,merchant_code,card_last_numbers);
    }

    private void handlePaymentFailure(String errorDescription) {
        paymentVerified = true;
        verificationHandler.removeCallbacks(verificationRunnable);

        requireActivity().runOnUiThread(() -> {
            if (qrDialog != null && qrDialog.isShowing()) {
                qrDialog.dismiss();
            }

            new AlertDialog.Builder(requireContext())
                    .setTitle("Pago no procesado")
                    .setMessage(errorDescription)
                    .setPositiveButton("Aceptar", null)
                    .setCancelable(false)
                    .show();
        });
    }
//#endregion


    private void mostrarSpinnerProcesandoPago() {
        requireActivity().runOnUiThread(() -> {
            progressBarClienteBuscar.setVisibility(View.VISIBLE);
        });
    }

//#region grabarFactura
    private void grabarFactura(List<Producto> productos, String ticket_number, String merchant_code, String card_last_numbers) {
        FacturaManager facturaManager = new FacturaManager(requireContext(), token, baseUrl, CodEmpresa, CodSucursal);
        progressBarClienteBuscar.setVisibility(View.VISIBLE);

        facturaManager.grabarFactura(codigoCliente, productos, ticket_number, merchant_code, card_last_numbers, currentHookAlias, new FacturaManager.FacturaCallback() {
            @Override
            public void onSuccess(JSONObject response) {
                requireActivity().runOnUiThread(() -> {
                    try {
                        Log.d("FacturaResponse", "Respuesta exitosa: " + response.toString());
                        progressBarClienteBuscar.setVisibility(View.GONE);

                        // Verificar si hay algún código de error en el JSON
                        if (response.has("codigoEstado") && response.getInt("codigoEstado") != 200) {
                            String errorMsg = "Error en la API: ";
                            if (response.has("cuerpo")) {
                                errorMsg += response.getString("cuerpo");
                            } else {
                                errorMsg += response.toString();
                            }
                       
                            String responseForAudit = response.toString();
                            llamarAuditoriaTransaccion(productos, responseForAudit, ticket_number, merchant_code, card_last_numbers);
                            new AlertDialog.Builder(requireContext())
                            .setTitle("Pago Procesado")
                            .setMessage("Hubo un error con la factura pero el pago fue exitoso, verifique con el administrador despues")
                            .setPositiveButton("OK", (dialog, which) -> {
                                limpiarCarrito();
                                NavController navController = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
                                navController.navigate(R.id.nav_inputProduct);
                            })
                            .setCancelable(false)
                            .show();
                            return;
                        }

                        Toast.makeText(getContext(), "Pago confirmado y factura generada", Toast.LENGTH_LONG).show();
                        limpiarCarrito();
                        enviarEmail.enviarEmail(requireContext(), response, true, false, null);
                        NavController navController = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
                        navController.navigate(R.id.action_to_Fragment_pay);
                    } catch (JSONException e) {
                        onError(new VolleyError("Error al procesar respuesta JSON"));
                    }
                });
            }


            @Override
            public void onError(VolleyError error) {
                requireActivity().runOnUiThread(() -> {
                    progressBarClienteBuscar.setVisibility(View.GONE);
                    String errorMessage = "Error al grabar factura";

                    if (error.networkResponse != null && error.networkResponse.data != null) {
                        try {
                            String errorBody = new String(error.networkResponse.data, "utf-8");
                            JSONObject errorJson = new JSONObject(errorBody);
                            if (errorJson.has("cuerpo")) {
                                errorMessage += ": " + errorJson.getString("cuerpo");
                            } else {
                                errorMessage += " (Código: " + error.networkResponse.statusCode + ")";
                            }
                        } catch (Exception e) {
                            errorMessage += ": " + new String(error.networkResponse.data);
                        }
                    } else if (error.getMessage() != null) {
                        errorMessage += ": " + error.getMessage();
                    }

                    Log.e("FacturaError", errorMessage);
                    String responseForAudit = error.toString();
                    llamarAuditoriaTransaccion(productos, responseForAudit, ticket_number, merchant_code, card_last_numbers);
                    new AlertDialog.Builder(requireContext())
                    .setTitle("Pago Procesado")
                    .setMessage("Hubo un error con la factura pero el pago fue exitoso, verifique con el administrador despues")
                    .setPositiveButton("OK", (dialog, which) -> {
                        limpiarCarrito();
                        NavController navController = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
                        navController.navigate(R.id.nav_inputProduct);
                    })
                    .setCancelable(false)
                    .show();
                });
            }
        });
    }
//#endregion

//#region revertirPagoSiEsNecesario
    private void revertirPagoSiEsNecesario() {
        if (currentHookAlias == null || !shouldContinueVerification) {
            Log.d("REVERT", "Reversión cancelada - Condiciones no cumplidas");
            return;
        }

        Log.d("REVERT", "Iniciando reversión por timeout");
        progressBarClienteBuscar.setVisibility(View.VISIBLE);

        Call<RevertResponse> call = bancardApiService.revertPayment(
                getCommerceCode(),
                getCommerceBranch(),
                currentHookAlias,
                getBancardAuthHeader()
        );

        call.enqueue(new Callback<RevertResponse>() {
            @Override
            public void onResponse(Call<RevertResponse> call, Response<RevertResponse> response) {
                progressBarClienteBuscar.setVisibility(View.GONE);
                if( response.isSuccessful() ){
                    Log.d("REVERT", "Pago revertido exitosamente");
                    shouldContinueVerification = false;
                }
            }

            @Override
            public void onFailure(Call<RevertResponse> call, Throwable t) {
                progressBarClienteBuscar.setVisibility(View.GONE);
                Log.e("REVERT_ERROR", "Error en la reversión: " + t.getMessage());
            }
        });
    }
//#endregion

    // Clase interna para manejar el callback de reversión
    private class RevertCallback implements Callback<RevertResponse> {
        @Override
        public void onResponse(Call<RevertResponse> call, Response<RevertResponse> response) {
            // Manejar respuesta de reversa
            if (response.isSuccessful()) {
                Log.d("REVERT", "Pago revertido exitosamente");
            } else {
                try {
                    okhttp3.ResponseBody errorBody = response.errorBody();
                    String errorContent = errorBody != null ? errorBody.string() : "null";
                    Log.e("REVERT_ERROR", "Error al revertir: " + response.code() + " - " + errorContent);
                } catch (IOException e) {
                    Log.e("REVERT_ERROR", "Error leyendo errorBody", e);
                }
            }
        }

        @Override
        public void onFailure(Call<RevertResponse> call, Throwable t) {
            // Manejar fallo en reversa
            Log.e("REVERT_ERROR", "Error en la conexión: " + t.getMessage());
        }
    }

    private void generarReciboPago(List<Producto> productos, JSONObject paymentData) {
        SharedPreferences sharedPreferences = requireContext().getSharedPreferences("ConfigDB", Context.MODE_PRIVATE);
        String puestoventa = sharedPreferences.getString("puntoventa", "");

        generadorRecibos.generarRecibo(requireContext(), paymentData, puestoventa, new generadorRecibos.ReciboCallback() {
            @Override
            public void onSuccess(String pdfClientePath, String pdfComercioPath) {
                reciboGenerado = true;
                reciboPdfClientePath = pdfClientePath;
                reciboPdfComercioPath = pdfComercioPath;

                if (viewModel != null) {
                    try {
                        JSONObject reciboInfo = new JSONObject();
                        reciboInfo.put("pdfComercioPath", pdfComercioPath);
                        reciboInfo.put("pdfClientePath", pdfClientePath);
                        reciboInfo.put("generated", true);
                        reciboInfo.put("amount", paymentData.optString("amount"));
                        reciboInfo.put("ticket_number", paymentData.optString("ticket_number"));
                        viewModel.setReciboData(reciboInfo);
                    } catch (JSONException e) {
                        Log.e("RECIBO_ERROR", "Error creando reciboInfo", e);
                    }
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e("RECIBO_ERROR", "Error generando recibo: " + errorMessage);
            }
        });
    }

    private void generarImprimirFactura(JSONObject facturaData) {
        generadorFacturas.generarFactura(requireContext(), facturaData, pdfGuardado, new generadorFacturas.FacturaCallback() {
            @Override
            public void onSuccess(String pdfFilePath) {
                pdfGuardado = new File(pdfFilePath);
                Log.d("FACTURA_CLIENT", "Factura generada: " + pdfFilePath);

                impresion.imprimirPDFDesdeArchivo(requireContext(), pdfFilePath, "Factura de compra", new impresion.PrintCallback() {
                    @Override
                    public void onSuccess() {
                        Log.d("FACTURA_CLIENT", "Factura impresa exitosamente");
                    }

                    @Override
                    public void onError(String errorMessage) {
                        Log.e("FACTURA_CLIENT", "Error al imprimir factura: " + errorMessage);
                    }
                });
            }

            @Override
            public void onError(String errorMessage) {
                Log.e("FACTURA_CLIENT", "Error al generar factura: " + errorMessage);
            }
        });
    }

//#region llamarAuditoriaTransaccion
    private void llamarAuditoriaTransaccion(List<Producto> productos, String logResponse, String ticket_number, String merchant_code, String card_last_numbers) {
        try {
            SharedPreferences prefs = requireContext().getSharedPreferences("ConfigDB", Context.MODE_PRIVATE);

            // Obtener datos básicos
            String puntoVenta = prefs.getString("puntoventa", "MOV1-SUC03");
            String prefijo = prefs.getString("prefijo_puntoventa", null);

            String codigoVendedor = prefs.getString("codigovendedor", null);
            String codEmp = CodEmpresa != null ? CodEmpresa : prefs.getString("CodEmpresa", null);
            String codSuc = CodSucursal != null ? CodSucursal : prefs.getString("CodSucursal", null);

            // Calcular total
            double total = 0;
            for (Producto producto : productos) {
                double precio = Double.parseDouble(producto.getPrecio());
                double cantidad = Double.parseDouble(producto.getCantidad());
                total += precio * cantidad;
            }

            // Obtener fecha actual
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault());
            String fechaActual = sdf.format(new java.util.Date());

            // Intentar extraer IDCOMPROBANTE del response si está disponible
            String idComprobante = null;

            try {
                if (logResponse.contains("IdComprobante:")) {
                    int startIndex = logResponse.indexOf("IdComprobante:") + "IdComprobante:".length();
                    String remaining = logResponse.substring(startIndex);
                    StringBuilder sb = new StringBuilder();
                    for (char c : remaining.toCharArray()) {
                        if (Character.isDigit(c)) {
                            sb.append(c);
                        } else {
                            break;
                        }
                    }
                    if (sb.length() > 0) {
                        idComprobante = sb.toString();
                      }
                }



            } catch (Exception e) {
                Log.e("AUDIT", "Error extrayendo IDCOMPROBANTE o Numero: " + e.getMessage());
            }

            // Primera llamada: SP_INSERT_AUDIT_TRANSAC
            JSONObject auditTransac = new JSONObject();
            auditTransac.put("codemp", codEmp);
            auditTransac.put("codsuc", codSuc);
            if(ParametrosConf.TEST_MODE_VALUE.equals("true")) {

                auditTransac.put("TEST", "true");
                auditTransac.put("TESTPC", ParametrosConf.TEST_PC_VALUE);

            }
            auditTransac.put("proc", "SP_INSERT_AUDIT_TRANSAC");

            JSONObject params = new JSONObject();
            params.put("@IDCOMPROBANTE", idComprobante);
            params.put("@CODCMP", "FB");
            params.put("@PREFIJO", prefijo.trim());
            params.put("@FECHA", fechaActual);
            params.put("@OPERADOR", codigoVendedor.trim());
            params.put("@TOTAL", String.format(java.util.Locale.US, "%.2f", total));
            params.put("@TIMESTAMP", fechaActual);
            params.put("@HOSTNAME", puntoVenta.trim());
            params.put("@ACCION", "X");
            params.put("@LOGAPP", logResponse);
            params.put("@HOOKALIAS", currentHookAlias != null ? currentHookAlias : "");

            auditTransac.put("params", params);

            Log.d("AUDIT", "Enviando auditoría transacción: " + auditTransac.toString());

            RequestQueue queue = Volley.newRequestQueue(requireContext());
            String finalIdComprobante = idComprobante;
            JsonObjectRequest jsonRequest = new JsonObjectRequest(
                    com.android.volley.Request.Method.POST,
                    (BASE_URL+"webservice.php"),
                    auditTransac,
                    response -> {
                        Log.d("AUDIT", "Auditoría transacción exitosa: " + response.toString());

                        llamarAuditoriaItems(productos, finalIdComprobante, codEmp, codSuc);
                    },
                    error -> {
                        Log.e("AUDIT", "Error en auditoría transacción: " + error.toString());
                        // Continuar con items aunque falle la primera
                        llamarAuditoriaItems(productos, finalIdComprobante, codEmp, codSuc);
                    }
            );

            jsonRequest.setRetryPolicy(new DefaultRetryPolicy(
                    10000,
                    1,
                    DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
            ));

            queue.add(jsonRequest);

        } catch (Exception e) {
            Log.e("AUDIT", "Error preparando auditoría: " + e.getMessage());
        }
    }
//#endregion

//#region llamarAuditoriaItems
    private void llamarAuditoriaItems(List<Producto> productos, String idComprobante, String codEmp, String codSuc) {
        try {
            RequestQueue queue = Volley.newRequestQueue(requireContext());

            // Hacer una llamada por cada producto
            for (Producto producto : productos) {
                JSONObject auditItems = new JSONObject();
                auditItems.put("codemp", codEmp);
                auditItems.put("codsuc", codSuc);
                auditItems.put("proc", "SP_INSERT_AUDIT_TRANSAC_ITEMS");

                if(ParametrosConf.TEST_MODE_VALUE.equals("true")) {

                    auditItems.put("TEST", "true");
                    auditItems.put("TESTPC", ParametrosConf.TEST_PC_VALUE);

                }

                // Formar código final como en FacturaManager
                String codigo_var = producto.getCodigo().trim();
                String talla_var = producto.getTalla().trim();
                String codigo_final = codigo_var + "0," + talla_var;

                // Calcular importe total
                double precio = Double.parseDouble(producto.getPrecio());
                double cantidad = Double.parseDouble(producto.getCantidad());
                double importeTotal = precio * cantidad;

                JSONObject params = new JSONObject();
                params.put("@IDCOMPROBANTE", idComprobante);
                params.put("@CODITM", codigo_final);
                params.put("@CANTIDAD1", producto.getCantidad());
                params.put("@PRECIO", producto.getPrecio());
                params.put("@IMPORTE", String.format(java.util.Locale.US, "%.2f", importeTotal));

                auditItems.put("params", params);

                Log.d("AUDIT", "Enviando auditoría item: " + auditItems.toString());

                JsonObjectRequest jsonRequest = new JsonObjectRequest(
                        com.android.volley.Request.Method.POST,
                        (BASE_URL+"webservice.php"),
                        auditItems,
                        response -> {
                            Log.d("AUDIT", "Auditoría item exitosa: " + response.toString());
                        },
                        error -> {
                            Log.e("AUDIT", "Error en auditoría item: " + error.toString());
                        }
                );

                jsonRequest.setRetryPolicy(new DefaultRetryPolicy(
                        10000,
                        1,
                        DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
                ));

                queue.add(jsonRequest);
            }

        } catch (Exception e) {
            Log.e("AUDIT", "Error preparando auditoría items: " + e.getMessage());
        }
    }
//#endregion


    // Interface para la API de Bancard
    public interface BancardApiService {
        @POST("commerces/{commerce_code}/branches/{commerce_branch}/selling/generate-qr-express")
        Call<QRResponse> generateQR(
                @Path("commerce_code") String commerceCode,
                @Path("commerce_branch") String commerce_branch,
                @Body QRRequest qrRequest,
                @Header("Authorization") String authHeader

        );

        @GET("commerces/{commerce_code}/branches/{commerce_branch}/payments/{hook_alias}")
        Call<PaymentStatusResponse> checkPaymentStatus(
                @Path("commerce_code") String commerceCode,
                @Path("commerce_branch") String commerce_branch,
                @Path("hook_alias") String hookAlias,
                @Header("Authorization") String authHeader,
                @Header("Content-Type") String contentType
        );

        @PUT("commerces/{commerce_code}/branches/{commerce_branch}/selling/payments/revert/{hook_alias}")
        Call<RevertResponse> revertPayment(
                @Path("commerce_code") String commerceCode,
                @Path("commerce_branch") String commerce_branch,
                @Path("hook_alias") String hookAlias,
                @Header("Authorization") String authHeader
        );
    }

    public class QRRequest {
        private double amount;
        private String description;
        private List<Promotion> promotions;  // Opcional, si necesitas promociones
        private String qr_source;           // Opcional ("QR-Change" para vuelto)
        private Double amount_buy;          // Opcional (para vuelto)
        private Double amount_change;       // Opcional (para vuelto)

        // Constructor vacío (necesario para Retrofit)
        public QRRequest() {}

        // Getters y Setters
        public double getAmount() {
            return amount;
        }

        public void setAmount(double amount) {
            this.amount = amount;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        // ... otros getters y setters si son necesarios
    }

    public class Promotion {
        private String bin;
        private String ent_code;
        private String client;
        private String brand;
        private String product;
        private String affinity;

        // Getters y Setters
    }

    public class QRResponse {
        private String status;
        private QRExpress qr_express; // Nombre debe coincidir con la respuesta JSON
        private List<SupportedClient> supported_clients;

        @SerializedName("status")

        // Getters
        public QRExpress getQrExpress() {
            return qr_express;
        }

        public String getStatus() {
            return status;
        }


        // Clase interna
        public class QRExpress {
            private String amount;
            private String hook_alias;
            private String description;
            private String url;
            private String created_at;
            private String qr_data;



            public String getHookAlias() {
                return hook_alias;
            }

            public String getAmount() {
                return amount;
            }

            public String getDescription() {
                return description;
            }

            public String getUrl() {
                return url;
            }

            public String getQrData() {
                return qr_data;
            }

            public String getCreatedAt() {
                return created_at;
            }

        }

        public class SupportedClient {
            private String name;
            private String logo_url;

            // Getters
        }
    }

    public class QRExpress {
        private String amount;
        private String hook_alias;
        private String description;
        private String url;  // Asegúrate que este campo existe
        private String created_at;
        private String qr_data;

        // Constructor
        public QRExpress() {}

        // Getters y Setters para todos los campos
        public String getAmount() {
            return amount;
        }

        public String getHookAlias() {
            return hook_alias;
        }

        public String getDescription() {
            return description;
        }

        public String getUrl() {  // Este es el método que necesitas
            return url;
        }

        public String getCreatedAt() {
            return created_at;
        }

        public String getQrData() {
            return qr_data;
        }
    }

    public static class RevertResponse {
        private String status;

        // Getters y setters
    }



    public class PaymentStatusResponse {
        @SerializedName("status")
        private String paymentStatus;

        @SerializedName("response_code")
        private String code;

        @SerializedName("response_description")
        private String description;

        public String getResponseDescription() {
            return description;
        }

        public String getStatus() {
            return paymentStatus;
        }


    }


    private void obtenerToken() {
        TokenRequest tokenRequest = new TokenRequest(requireContext(), new TokenRequest.TokenRequestListener() {
            @Override
            public void onTokenReceived(String receivedToken) {
                token = receivedToken;
            }
        });
        tokenRequest.execute();
    }

//#region consultarCliente
    private void consultarCliente(String nroDocumento, Runnable onSuccess) {
        Log.d("DEBUG", "Entrando en consultarCliente con nroDocumento: " + nroDocumento);
        progressBarClienteBuscar.setVisibility(View.VISIBLE);
        String url = API1_URL + "/api/CLIENTE?NumeroDocumento1=" + nroDocumento;
        Log.d("DEBUG", "URL de la solicitud: " + url);
        Log.d("DEBUG", "URL de la solicitud: " + url);
        RequestQueue queue = Volley.newRequestQueue(requireContext());

        StringRequest stringRequest = new StringRequest(com.android.volley.Request.Method.GET, url,
                response -> {
                    try {
                        JSONObject jsonResponse = new JSONObject(response);

                        // Verificar si CLIENTES es null
                        if (jsonResponse.isNull("CLIENTES")) {
                            Log.d("DEBUG", "Cliente no encontrado en la base de datos.");
                            progressBarClienteBuscar.setVisibility(View.GONE);
                            mostrarDialogoCreacionCliente(nroDocumento); // Mostrar diálogo para crear cliente
                            return; // Salir del método
                        }

                        JSONObject clientesObj = jsonResponse.getJSONObject("CLIENTES");

                        Object clienteData = clientesObj.get("Cliente"); // Puede ser JSONArray o JSONObject

                        if (clienteData instanceof JSONArray) {
                            JSONArray clientesArray = (JSONArray) clienteData;
                            if (clientesArray.length() > 0) {
                                JSONObject cliente = clientesArray.getJSONObject(0);
                                codigoCliente = cliente.getString("Codigo");
                                String nombre = cliente.getString("Nombre");
                                String correo = cliente.optString("Email", "");
                                textNombre.setText(nombre);
                                textCorreo.setText(correo);
                                btnQr.setVisibility(View.VISIBLE);
                                btnModificar.setVisibility(View.VISIBLE);
                                progressBarClienteBuscar.setVisibility(View.GONE);
                                Log.d("Cliente", "Nombre: " + nombre);
                                Log.d("Cliente","Codigo: " + codigoCliente);
                            }
                        } else if (clienteData instanceof JSONObject) {
                            JSONObject cliente = (JSONObject) clienteData;
                            String nombre = cliente.getString("Nombre");
                            codigoCliente = cliente.getString("Codigo");
                            String correo = cliente.optString("Email", "");
                            textNombre.setText(nombre);
                            textCorreo.setText(correo);
                            btnQr.setVisibility(View.VISIBLE);
                            btnModificar.setVisibility(View.VISIBLE);
                            progressBarClienteBuscar.setVisibility(View.GONE);
                            Log.d("Cliente", "Nombre: " + nombre);
                            Log.d("Cliente","Codigo: " + codigoCliente);
                        }

                    } catch (JSONException e) {
                        Log.e("JSONError", "Error procesando JSON", e);
                        progressBarClienteBuscar.setVisibility(View.GONE);
                        mostrarDialogoCreacionCliente(nroDocumento); // Mostrar diálogo para crear cliente
                    }

                },
                error -> {
                    // Manejo de errores
                    if (error instanceof TimeoutError) {
                        // Error de timeout
                        Toast.makeText(getContext(), "La solicitud tardó demasiado. Por favor, intenta de nuevo.", Toast.LENGTH_LONG).show();
                        Log.e("API_ERROR", "TimeoutError: La solicitud tardó demasiado.");
                    } else if (error.networkResponse != null) {
                        // Error de red o del servidor
                        String errorMessage = "Error en la solicitud: ";
                        switch (error.networkResponse.statusCode) {
                            case 400:
                                errorMessage += "Solicitud incorrecta.";
                                break;
                            case 401:
                                errorMessage += "No autorizado.";
                                break;
                            case 404:
                                errorMessage += "Cliente no encontrado.";
                                break;
                            case 500:
                                errorMessage += "Error interno del servidor.";
                                break;
                            default:
                                errorMessage += "Código de estado: " + error.networkResponse.statusCode;
                                break;
                        }
                        Toast.makeText(getContext(), errorMessage, Toast.LENGTH_LONG).show();
                        Log.e("API_ERROR", errorMessage);
                    } else {
                        // Otros errores
                        Toast.makeText(getContext(), "Error desconocido. Por favor, intenta de nuevo.", Toast.LENGTH_LONG).show();
                        progressBarClienteBuscar.setVisibility(View.GONE);
                        Log.e("API_ERROR", "Error desconocido: " + error.toString());
                    }

                    // Mostrar diálogo para crear cliente en caso de error
                    mostrarDialogoCreacionCliente(nroDocumento);
                }) {
            @Override
            public Map<String, String> getHeaders() throws AuthFailureError {
                Map<String, String> headers = new HashMap<>();
                headers.put("Authorization", "Bearer " + token);
                headers.put("accept", "*/*");
                return headers;
            }
        };

        // Configurar el tiempo de espera a 10 segundos
        stringRequest.setRetryPolicy(new DefaultRetryPolicy(
                10000, // Tiempo de espera en milisegundos (10 segundos)
                DefaultRetryPolicy.DEFAULT_MAX_RETRIES, // Número de reintentos
                DefaultRetryPolicy.DEFAULT_BACKOFF_MULT) // Multiplicador de reintento
        );

        queue.add(stringRequest);
        Log.d("DEBUG", "Petición agregada a la cola de Volley.");
    }
//#endregion

    private void mostrarDialogoCreacionCliente(String nroDocumento) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        progressBarClienteBuscar.setVisibility(View.GONE);
        builder.setTitle("Confirmación")
                .setMessage("¿Número de Documento no existe, desea crearlo?")
                .setPositiveButton("Sí", (dialog, which) -> {
                    Bundle args = new Bundle();
                    args.putString("nroDocumento", nroDocumento);
                    args.putBoolean("abiertoDesdeMenu", false);
                    args.putBoolean("nuevo", true);
                    NavController navController = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
                    navController.navigate(R.id.nav_clientFragment, args); // <- Cambio crucial aquí
                })
                .setNegativeButton("No", (dialog, which) -> {
                    // Acción cuando se selecciona "No"
                })
                .show();
    }

    private void mostrarDialogoModificacionCliente(String nroDocumento) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        progressBarClienteBuscar.setVisibility(View.GONE);
        builder.setTitle("Confirmación")
                .setMessage("¿Desea modificar el cliente?")
                .setPositiveButton("Sí", (dialog, which) -> {
                    Bundle args = new Bundle();
                    args.putString("nroDocumento", nroDocumento);
                    args.putBoolean("abiertoDesdeMenu", false);
                    args.putBoolean("nuevo", false);
                    NavController navController = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
                    navController.navigate(R.id.nav_clientFragment, args);
                })
                .setNegativeButton("No", (dialog, which) -> {
                    // Acción cuando se selecciona "No"
                })
                .show();
    }


    private List<Producto> obtenerProductosDelCarrito() {
        List<Producto> productos = new ArrayList<>();
        if (viewModel.getProductListLiveData().getValue() != null) {
            for (Product product : viewModel.getProductListLiveData().getValue()) {
                productos.add(new Producto(
                        product.getCode(),
                        product.getSize(),
                        String.valueOf(product.getPrice()),
                        String.valueOf(product.getQuantity())
                ));
            }
        }
        return productos;
    }


    private void limpiarCarrito() {
        // Limpiar la lista en el ViewModel

        viewModel.limpiarCarrito(); // Llama al método público del ViewModel

    }

    @Override
    public void onTokenReceived(String token) {
        this.token = token;
    }


    private SSLSocketFactory getSSLSocketFactory() {
        try {
            // Crea un TrustManager que no valide certificados
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return new java.security.cert.X509Certificate[]{};
                        }
                    }
            };

            // Configura SSL para aceptar todos los certificados
            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            return sslContext.getSocketFactory();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private X509TrustManager getTrustManager() {
        return new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[]{};
            }
        };
    }

    private void hideProgressBar() {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                if (progressBarClienteBuscar != null) {
                    progressBarClienteBuscar.setVisibility(View.GONE);
                }
            });
        }
    }

//#region registrarHookAlias
    private void registrarHookAlias(String hookAlias, double amount, String description, String createdAt) {
        // Crear el JSON body una sola vez
        JSONObject jsonBody = new JSONObject();
        try {
            jsonBody.put("hookalias", hookAlias);
            jsonBody.put("amount", (int) amount);
            jsonBody.put("description", description);
            jsonBody.put("created_at", createdAt);
        } catch (JSONException e) {
            Log.e("REGISTRAR_ALIAS", "Error al crear JSON", e);
            return;
        }

        RequestQueue queue = Volley.newRequestQueue(requireContext());
        String urlRequest = BASE_URL + "registrarAlias.php";
        Log.d("REGISTRAR_ALIAS", "URL de la solicitud: " + urlRequest);
        JsonObjectRequest putRequest = new JsonObjectRequest(
                com.android.volley.Request.Method.PUT, urlRequest, jsonBody,
                response -> Log.d("REGISTRAR_ALIAS", "Alias registrado con éxito: " + response),
                error -> Log.e("REGISTRAR_ALIAS", "Error al registrar alias: " + error.toString())
        );
        queue.add(putRequest);

    }
//#endregion



}
