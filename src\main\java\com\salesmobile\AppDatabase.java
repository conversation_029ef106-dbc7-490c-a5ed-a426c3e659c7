package com.salesmobile;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.salesmobile.ui.factura.Venta;
import com.salesmobile.ui.factura.VentaDao;
import com.salesmobile.Puntosdeventas;
import com.salesmobile.PuntosdeventasDao;

@Database(entities = {Configuracion.class, Venta.class, Puntosdeventas.class},
        version = 5,
        exportSchema = false)
public abstract class AppDatabase extends RoomDatabase {

    private static volatile AppDatabase INSTANCE;

    public abstract VentaDao ventaDao();
    public abstract ConfiguracionDao configuracionDao();
    public abstract PuntosdeventasDao puntosdeventasDao();

    public static AppDatabase getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (AppDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(context.getApplicationContext(),
                                    AppDatabase.class, "app_database")
                            .addMigrations(MIGRATION_RECREATE_PUNTOS, MIGRATION_3_4, MIGRATION_4_5)
                            .fallbackToDestructiveMigration()
                            .build();
                }
            }
        }
        return INSTANCE;
    }


    static final Migration MIGRATION_4_5 = new Migration(4, 5) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE vendedores ADD COLUMN soloconsulta TEXT");
        }
    };

    static final Migration MIGRATION_3_4 = new Migration(3, 4) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // Añadir los nuevos campos a la tabla configuraciones
            database.execSQL("ALTER TABLE configuraciones ADD COLUMN baseUrl2 TEXT");
            database.execSQL("ALTER TABLE configuraciones ADD COLUMN usuario2 TEXT");
            database.execSQL("ALTER TABLE configuraciones ADD COLUMN contrasena2 TEXT");
            database.execSQL("ALTER TABLE configuraciones ADD COLUMN grantType2 TEXT");
            database.execSQL("ALTER TABLE configuraciones ADD COLUMN codigoEmpresa2 TEXT");
            database.execSQL("ALTER TABLE configuraciones ADD COLUMN codigoSucursal2 TEXT");
        }
    };


    static final Migration MIGRATION_RECREATE_PUNTOS = new Migration(2, 3) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {

        }
    };


    static final Migration MIGRATION_RECREATE_VENTAS = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // 1. Crear tabla temporal
            database.execSQL("CREATE TABLE IF NOT EXISTS ventas_temp AS SELECT * FROM ventas");

            // 2. Eliminar tabla original
            database.execSQL("DROP TABLE IF EXISTS ventas");

            // 3. Crear nueva tabla con esquema correcto
            database.execSQL("CREATE TABLE IF NOT EXISTS ventas (ID INTEGER PRIMARY KEY, FECHA TEXT, TOTAL REAL NOT NULL, NUM_TRANSACCIONES INTEGER NOT NULL)");

            // 4. Copiar datos
            database.execSQL("INSERT INTO ventas SELECT * FROM ventas_temp");

            // 5. Eliminar temporal
            database.execSQL("DROP TABLE IF EXISTS ventas_temp");
        }
    };

}