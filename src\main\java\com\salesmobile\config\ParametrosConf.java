package com.salesmobile.config;

import android.content.Context;
import android.util.Log;

public class ParametrosConf {

    private static SharedPreferences sharedPreferences;
    private static SharedPreferences.ParametrosInfo parametros;
    private static java.util.List<SharedPreferences.ConfiguracionSincronizada> configuraciones;

    // Variables estáticas públicas para acceso directo (reemplaza AppConfig)
    public static String ENV_STAGE;
    public static String TEST_MODE_VALUE;
    public static String TEST_PC_VALUE;
    public static String BANCARD_BASE_URL;
    public static String BANCARD_PUBLIC_KEY;
    public static String BANCARD_PRIVATE_KEY;
    public static String COMMERCE_CODE;
    public static String COMMERCE_BRANCH;
    public static String APPMOVIL_BASE_URL;
    public static String BASE_URL;
    public static String VER_CONFIG;

    public static String API1_URL;
    public static String API2_URL;
    public static String COD_SUCURSAL;
    public static String COD_EMPRESA;
    public static String PUNTOVENTA;

    public static void init(Context context) {
        sharedPreferences = new SharedPreferences(context);
        parametros = sharedPreferences.getParametros();
        configuraciones = sharedPreferences.getSyncedConfiguraciones();
        updateValues();
    }

    public static void refresh() {
        if (sharedPreferences != null) {
            parametros = sharedPreferences.getParametros();
            configuraciones = sharedPreferences.getSyncedConfiguraciones();
            updateValues();
        }
    }

    private static void updateValues() {
        if (parametros != null) {
            ENV_STAGE = trimString(parametros.getEntorno());
            TEST_MODE_VALUE = trimString(parametros.getTest());
            TEST_PC_VALUE = trimString(parametros.getTestPc());

            String devBancardUrl = trimString(parametros.getDevBancardUrl());
            String devBancardPublicKey = trimString(parametros.getDevBancardPublicKey());
            String devBancardPrivateKey = trimString(parametros.getDevBancardPrivateKey());
            String prodBancardUrl = trimString(parametros.getProdBancardUrl());
            String prodBancardPublicKey = trimString(parametros.getProdBancardPublicKey());
            String prodBancardPrivateKey = trimString(parametros.getProdBancardPrivateKey());
            String appmovilDefaultUrl = trimString(parametros.getAppmovilDefaultUrl());
            String bancardCommerceBranch = String.valueOf(parametros.getBancardCommerceBranch());
            String bancardCommerceCode = String.valueOf(parametros.getBancardCommerceCode());
            String devDefaultUrl = trimString(parametros.getDevDefaultUrl());
            String prodDefaultUrl = trimString(parametros.getProdDefaultUrl());

            // Aplicar lógica para determinar URLs según entorno
            boolean isDev = "DEV".equals(ENV_STAGE);
            BANCARD_BASE_URL = isDev ? devBancardUrl : prodBancardUrl;
            BANCARD_PUBLIC_KEY = isDev ? devBancardPublicKey : prodBancardPublicKey;
            BANCARD_PRIVATE_KEY = isDev ? devBancardPrivateKey : prodBancardPrivateKey;
            COMMERCE_CODE = bancardCommerceCode;
            COMMERCE_BRANCH = bancardCommerceBranch;
            APPMOVIL_BASE_URL = appmovilDefaultUrl;
            BASE_URL = isDev ? devDefaultUrl : prodDefaultUrl;
            VER_CONFIG = String.valueOf(parametros.getVerConfig());

            if (configuraciones != null && !configuraciones.isEmpty()) {
                SharedPreferences.ConfiguracionSincronizada config = configuraciones.get(0);
                API1_URL = trimString(config.getUrlapi());
                API2_URL = trimString(config.getUrlapi2());
                COD_EMPRESA = trimString(config.getCodempresa());
                COD_SUCURSAL = trimString(config.getCodsucursal());
            }

            // Obtener PUNTOVENTA desde SharedPreferences
            if (sharedPreferences != null) {
                android.content.SharedPreferences configDbPrefs = sharedPreferences.getConfigDbPrefs();
                PUNTOVENTA = trimString(configDbPrefs.getString("puntoventa", ""));
            }

        } else {
            ENV_STAGE = null;
            TEST_MODE_VALUE = null;
            TEST_PC_VALUE = null;
            BANCARD_BASE_URL = null;
            BANCARD_PUBLIC_KEY = null;
            BANCARD_PRIVATE_KEY = null;
            COMMERCE_CODE = null;
            COMMERCE_BRANCH = null;
            APPMOVIL_BASE_URL = null;
            BASE_URL = null;
            VER_CONFIG = null;
            API1_URL = null;
            API2_URL = null;
            COD_EMPRESA = null;
            COD_SUCURSAL = null;
            PUNTOVENTA = null;
        }
    }

    // Método helper para aplicar trim a strings, manejando valores null
    private static String trimString(String value) {
        return value != null ? value.trim() : null;
    }

    /**
     * Información de debug sobre el estado actual de configuración
     */
    public static String getDebugInfo() {
        return String.format("Entorno: %s, isDev: %s, BaseURL: %s, BancardURL: %s",
                ENV_STAGE, "DEV".equals(ENV_STAGE), BASE_URL, BANCARD_BASE_URL);
    }
}
