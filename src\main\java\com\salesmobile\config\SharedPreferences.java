package com.salesmobile.config;

import android.content.Context;

/**
 * Clase centralizada para manejar SharedPreferences en toda la aplicación
 * Proporciona métodos utilitarios para acceder y modificar preferencias de manera consistente
 *
 * Maneja tres tipos principales de SharedPreferences:
 * - CONFIG_DB: Configuración general, credenciales, punto de venta, cotización
 * - APP_PREFS: Tokens de autenticación y datos de sesión
 * - SUCURSAL_INFO: Información detallada de la sucursal
 */
public class SharedPreferences {

    // Nombres de SharedPreferences
    private static final String CONFIG_DB = "ConfigDB";
    private static final String APP_PREFS = "AppPrefs";
    private static final String SUCURSAL_INFO = "SucursalInfo";

    // Keys para CONFIG_DB - Configuración de conexión
    public static final String KEY_SERVIDOR = "servidor";
    public static final String KEY_BASE_DATOS = "basedatos";
    public static final String KEY_PUNTO_VENTA = "puntoventa";

    // Keys para CONFIG_DB - Credenciales de usuario
    public static final String KEY_USUARIO_SQL = "usuarioSQL";
    public static final String KEY_PASSWORD_SQL = "passwordSQL";
    public static final String KEY_RECORDAR = "recordar";

    // Keys para CONFIG_DB - Información del punto de venta (después del login)
    public static final String KEY_ID_PUNTO_VENTA = "id_puntoventa";
    public static final String KEY_NOMBRE_PUNTO_VENTA = "nombre_puntoventa";
    public static final String KEY_PREFIJO_PUNTO_VENTA = "prefijo_puntoventa";

    // Keys para CONFIG_DB - Información del vendedor (después del login)
    public static final String KEY_CODIGO_VENDEDOR = "codigovendedor";
    public static final String KEY_SOLO_CONSULTA = "soloconsulta";

    // Keys para CONFIG_DB - Cotización del día
    public static final String KEY_VALOR1_INDICES = "valor1_indices";

    // Keys para APP_PREFS - Tokens de autenticación
    public static final String KEY_TOKEN = "token";
    public static final String KEY_TOKEN_EXPIRATION_TIME = "tokenExpirationTime";
    public static final String KEY_TOKEN2 = "token2";
    public static final String KEY_TOKEN_EXPIRATION_TIME2 = "tokenExpirationTime2";

    // Keys para SUCURSAL_INFO - Información de sucursal
    public static final String KEY_SUCURSAL_NOMBRE = "sucursal_nombre";
    public static final String KEY_SUCURSAL_DOMICILIO = "sucursal_domicilio";
    public static final String KEY_SUCURSAL_DOMICILIO2 = "sucursal_domicilio2";
    public static final String KEY_SUCURSAL_LOCALIDAD = "sucursal_localidad";
    public static final String KEY_SUCURSAL_TELEFONO = "sucursal_telefono";
    public static final String KEY_SUCURSAL_CODSUC = "sucursal_codsuc";

    // Keys para PARAMETROS - Configuración de parámetros
    public static final String KEY_PARAM_TEST = "param_test";
    public static final String KEY_PARAM_TESTPC = "param_testpc";
    public static final String KEY_PARAM_ENTORNO = "param_entorno";
    public static final String KEY_PARAM_DEV_BANCARD_URL = "param_dev_bancard_url";
    public static final String KEY_PARAM_DEV_BANCARD_PUBLIC_KEY = "param_dev_bancard_public_key";
    public static final String KEY_PARAM_DEV_BANCARD_PRIVATE_KEY = "param_dev_bancard_private_key";
    public static final String KEY_PARAM_PROD_BANCARD_URL = "param_prod_bancard_url";
    public static final String KEY_PARAM_PROD_BANCARD_PUBLIC_KEY = "param_prod_bancard_public_key";
    public static final String KEY_PARAM_PROD_BANCARD_PRIVATE_KEY = "param_prod_bancard_private_key";
    public static final String KEY_PARAM_DEV_DEFAULT_URL = "param_dev_default_url";
    public static final String KEY_PARAM_PROD_DEFAULT_URL = "param_prod_default_url";
    public static final String KEY_PARAM_BANCARD_COMMERCE_CODE = "param_bancard_commerce_code";
    public static final String KEY_PARAM_BANCARD_COMMERCE_BRANCH = "param_bancard_commerce_branch";
    public static final String KEY_PARAM_APPMOVIL_DEFAULT_URL = "param_appmovil_default_url";
    public static final String KEY_VER_CONFIG = "ver_config";

    // Keys para CONFIGURACIONES SINCRONIZADAS
    public static final String KEY_SYNC_CONFIG_PREFIX = "sync_config_";
    public static final String KEY_SYNC_CONFIG_COUNT = "sync_config_count";

    // Keys para VENDEDORES SINCRONIZADOS
    public static final String KEY_SYNC_VENDEDOR_PREFIX = "sync_vendedor_";
    public static final String KEY_SYNC_VENDEDOR_COUNT = "sync_vendedor_count";

    // Keys para PUNTOS DE VENTA SINCRONIZADOS
    public static final String KEY_SYNC_PV_PREFIX = "sync_pv_";
    public static final String KEY_SYNC_PV_COUNT = "sync_pv_count";

    //
    private final Context context;
    private final android.content.SharedPreferences configDbPrefs;
    private final android.content.SharedPreferences appPrefs;
    private final android.content.SharedPreferences sucursalInfoPrefs;

    /**
     * Constructor que inicializa todas las instancias de SharedPreferences
     * @param context Contexto de la aplicación
     */
    public SharedPreferences(Context context) {
        this.context = context.getApplicationContext();
        this.configDbPrefs = this.context.getSharedPreferences(CONFIG_DB, Context.MODE_PRIVATE);
        this.appPrefs = this.context.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE);
        this.sucursalInfoPrefs = this.context.getSharedPreferences(SUCURSAL_INFO, Context.MODE_PRIVATE);
    }

    // ========== MÉTODOS PARA CONFIG_DB ==========
    public android.content.SharedPreferences getConfigDbPrefs() {
        return configDbPrefs;
    }

    /**
     * Guarda la configuración de conexión a la base de datos
     */
    public void saveConnectionConfig(String servidor, String baseDatos, String puntoVenta) {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        editor.putString(KEY_SERVIDOR, servidor);
        editor.putString(KEY_BASE_DATOS, baseDatos);
        editor.putString(KEY_PUNTO_VENTA, puntoVenta);
        editor.apply();
    }

    public String getServidor() {
        return configDbPrefs.getString(KEY_SERVIDOR, "");
    }


    public String getBaseDatos() {
        return configDbPrefs.getString(KEY_BASE_DATOS, "");
    }

    public String getPuntoVenta() {
        return configDbPrefs.getString(KEY_PUNTO_VENTA, "");
    }


    public void saveUserCredentials(String usuarioSQL, String passwordSQL, boolean recordar) {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        if (recordar) {
            editor.putString(KEY_USUARIO_SQL, usuarioSQL);
            editor.putString(KEY_PASSWORD_SQL, passwordSQL);
            editor.putBoolean(KEY_RECORDAR, true);
        } else {
            editor.remove(KEY_USUARIO_SQL);
            editor.remove(KEY_PASSWORD_SQL);
            editor.putBoolean(KEY_RECORDAR, false);
        }
        editor.apply();
    }


    public UserCredentials getUserCredentials() {
        boolean recordar = configDbPrefs.getBoolean(KEY_RECORDAR, false);
        if (recordar) {
            String usuario = configDbPrefs.getString(KEY_USUARIO_SQL, "");
            String password = configDbPrefs.getString(KEY_PASSWORD_SQL, "");
            return new UserCredentials(usuario, password, recordar);
        }
        return new UserCredentials("", "", false);
    }


    public void savePuntoVentaInfo(int idPuntoVenta, String nombrePuntoVenta, String prefijoPuntoVenta) {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        editor.putInt(KEY_ID_PUNTO_VENTA, idPuntoVenta);
        editor.putString(KEY_NOMBRE_PUNTO_VENTA, nombrePuntoVenta);
        editor.putString(KEY_PREFIJO_PUNTO_VENTA, prefijoPuntoVenta);
        editor.apply();
    }


    public PuntoVentaInfo getPuntoVentaInfo() {
        int id = configDbPrefs.getInt(KEY_ID_PUNTO_VENTA, -1);
        String nombre = configDbPrefs.getString(KEY_NOMBRE_PUNTO_VENTA, "");
        String prefijo = configDbPrefs.getString(KEY_PREFIJO_PUNTO_VENTA, "");
        return new PuntoVentaInfo(id, nombre, prefijo);
    }

    public void saveVendedorInfo(String codigoVendedor, String soloConsulta) {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        editor.putString(KEY_CODIGO_VENDEDOR, codigoVendedor);
        editor.putString(KEY_SOLO_CONSULTA, soloConsulta);
        editor.apply();
    }

    public VendedorInfo getVendedorInfo() {
        String codigo = configDbPrefs.getString(KEY_CODIGO_VENDEDOR, "");
        String soloConsulta = configDbPrefs.getString(KEY_SOLO_CONSULTA, "N");
        return new VendedorInfo(codigo, soloConsulta);
    }

    public void saveCotizacion(float valor1Indices) {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        editor.putFloat(KEY_VALOR1_INDICES, valor1Indices);
        editor.apply();
    }


    public float getCotizacion() {
        return configDbPrefs.getFloat(KEY_VALOR1_INDICES, 1f);
    }

    // ========== MÉTODOS PARA APP_PREFS ==========
    public android.content.SharedPreferences getAppPrefs() {
        return appPrefs;
    }

    public void saveToken(String token, long expirationTime) {
        android.content.SharedPreferences.Editor editor = appPrefs.edit();
        editor.putString(KEY_TOKEN, token);
        editor.putLong(KEY_TOKEN_EXPIRATION_TIME, expirationTime);
        editor.apply();
    }

    public String getToken() {
        long expirationTime = appPrefs.getLong(KEY_TOKEN_EXPIRATION_TIME, 0);
        if (System.currentTimeMillis() < expirationTime) {
            return appPrefs.getString(KEY_TOKEN, null);
        }
        return null; // Token expirado
    }

    public void saveToken2(String token, long expirationTime) {
        android.content.SharedPreferences.Editor editor = appPrefs.edit();
        editor.putString(KEY_TOKEN2, token);
        editor.putLong(KEY_TOKEN_EXPIRATION_TIME2, expirationTime);
        editor.apply();
    }

    public String getToken2() {
        long expirationTime = appPrefs.getLong(KEY_TOKEN_EXPIRATION_TIME2, 0);
        if (System.currentTimeMillis() < expirationTime) {
            return appPrefs.getString(KEY_TOKEN2, null);
        }
        return null; // Token expirado
    }

    public boolean isTokenValid() {
        return getToken() != null;
    }

    public boolean isToken2Valid() {
        return getToken2() != null;
    }

    // ========== MÉTODOS PARA SUCURSAL_INFO ==========

    public android.content.SharedPreferences getSucursalInfoPrefs() {
        return sucursalInfoPrefs;
    }

    public void saveSucursalInfo(String nombre, String domicilio, String domicilio2,
                                String localidad, String telefono, int codsuc) {
        android.content.SharedPreferences.Editor editor = sucursalInfoPrefs.edit();
        editor.putString(KEY_SUCURSAL_NOMBRE, nombre);
        editor.putString(KEY_SUCURSAL_DOMICILIO, domicilio);
        editor.putString(KEY_SUCURSAL_DOMICILIO2, domicilio2);
        editor.putString(KEY_SUCURSAL_LOCALIDAD, localidad);
        editor.putString(KEY_SUCURSAL_TELEFONO, telefono);
        editor.putInt(KEY_SUCURSAL_CODSUC, codsuc);
        editor.apply();
    }

    public SucursalInfo getSucursalInfo() {
        String nombre = sucursalInfoPrefs.getString(KEY_SUCURSAL_NOMBRE, "");
        String domicilio = sucursalInfoPrefs.getString(KEY_SUCURSAL_DOMICILIO, "");
        String domicilio2 = sucursalInfoPrefs.getString(KEY_SUCURSAL_DOMICILIO2, "");
        String localidad = sucursalInfoPrefs.getString(KEY_SUCURSAL_LOCALIDAD, "");
        String telefono = sucursalInfoPrefs.getString(KEY_SUCURSAL_TELEFONO, "");
        int codsuc = sucursalInfoPrefs.getInt(KEY_SUCURSAL_CODSUC, 0);
        return new SucursalInfo(nombre, domicilio, domicilio2, localidad, telefono, codsuc);
    }

    public boolean hasSucursalInfo() {
        return !sucursalInfoPrefs.getString(KEY_SUCURSAL_NOMBRE, "").isEmpty();
    }

    public void clearSucursalInfo() {
        android.content.SharedPreferences.Editor editor = sucursalInfoPrefs.edit();
        editor.clear();
        editor.apply();
    }

    // ========== MÉTODOS PARA PARAMETROS ==========

    public void saveParametros(String test, String testPc, String entorno, String devBancardUrl,
                              String devBancardPublicKey, String devBancardPrivateKey,
                              String prodBancardUrl, String prodBancardPublicKey, String prodBancardPrivateKey,
                              String devDefaultUrl, String prodDefaultUrl, int bancardCommerceCode,
                              int bancardCommerceBranch, String appmovilDefaultUrl, int verConfig) {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        editor.putString(KEY_PARAM_TEST, test);
        editor.putString(KEY_PARAM_TESTPC, testPc);
        editor.putString(KEY_PARAM_ENTORNO, entorno);
        editor.putString(KEY_PARAM_DEV_BANCARD_URL, devBancardUrl);
        editor.putString(KEY_PARAM_DEV_BANCARD_PUBLIC_KEY, devBancardPublicKey);
        editor.putString(KEY_PARAM_DEV_BANCARD_PRIVATE_KEY, devBancardPrivateKey);
        editor.putString(KEY_PARAM_PROD_BANCARD_URL, prodBancardUrl);
        editor.putString(KEY_PARAM_PROD_BANCARD_PUBLIC_KEY, prodBancardPublicKey);
        editor.putString(KEY_PARAM_PROD_BANCARD_PRIVATE_KEY, prodBancardPrivateKey);
        editor.putString(KEY_PARAM_DEV_DEFAULT_URL, devDefaultUrl);
        editor.putString(KEY_PARAM_PROD_DEFAULT_URL, prodDefaultUrl);
        editor.putInt(KEY_PARAM_BANCARD_COMMERCE_CODE, bancardCommerceCode);
        editor.putInt(KEY_PARAM_BANCARD_COMMERCE_BRANCH, bancardCommerceBranch);
        editor.putString(KEY_PARAM_APPMOVIL_DEFAULT_URL, appmovilDefaultUrl);
        editor.putInt(KEY_VER_CONFIG, verConfig);
        editor.apply();
    }

    /**
     * Guarda parámetros aplicando la lógica de ParametrosConf para determinar URLs según entorno
     * Esto resuelve inconsistencias entre URLs de producción y testing
     */
    public void saveParametrosConLogica(String test, String testPc, String entorno, String devBancardUrl,
                                       String devBancardPublicKey, String devBancardPrivateKey,
                                       String prodBancardUrl, String prodBancardPublicKey, String prodBancardPrivateKey,
                                       String devDefaultUrl, String prodDefaultUrl, int bancardCommerceCode,
                                       int bancardCommerceBranch, String appmovilDefaultUrl, int verConfig) {

        // Guardar usando el método original
        saveParametros(test, testPc, entorno, devBancardUrl, devBancardPublicKey, devBancardPrivateKey,
                prodBancardUrl, prodBancardPublicKey, prodBancardPrivateKey, devDefaultUrl, prodDefaultUrl,
                bancardCommerceCode, bancardCommerceBranch, appmovilDefaultUrl, verConfig);

        // Refrescar ParametrosConf para que aplique la lógica inmediatamente
        ParametrosConf.refresh();
    }

    public ParametrosInfo getParametros() {
        String test = configDbPrefs.getString(KEY_PARAM_TEST, "");
        String testPc = configDbPrefs.getString(KEY_PARAM_TESTPC, "");
        String entorno = configDbPrefs.getString(KEY_PARAM_ENTORNO, "");
        String devBancardUrl = configDbPrefs.getString(KEY_PARAM_DEV_BANCARD_URL, "");
        String devBancardPublicKey = configDbPrefs.getString(KEY_PARAM_DEV_BANCARD_PUBLIC_KEY, "");
        String devBancardPrivateKey = configDbPrefs.getString(KEY_PARAM_DEV_BANCARD_PRIVATE_KEY, "");
        String prodBancardUrl = configDbPrefs.getString(KEY_PARAM_PROD_BANCARD_URL, "");
        String prodBancardPublicKey = configDbPrefs.getString(KEY_PARAM_PROD_BANCARD_PUBLIC_KEY, "");
        String prodBancardPrivateKey = configDbPrefs.getString(KEY_PARAM_PROD_BANCARD_PRIVATE_KEY, "");
        String devDefaultUrl = configDbPrefs.getString(KEY_PARAM_DEV_DEFAULT_URL, "");
        String prodDefaultUrl = configDbPrefs.getString(KEY_PARAM_PROD_DEFAULT_URL, "");
        int bancardCommerceCode = configDbPrefs.getInt(KEY_PARAM_BANCARD_COMMERCE_CODE, 0);
        int bancardCommerceBranch = configDbPrefs.getInt(KEY_PARAM_BANCARD_COMMERCE_BRANCH, 0);
        String appmovilDefaultUrl = configDbPrefs.getString(KEY_PARAM_APPMOVIL_DEFAULT_URL, "");
        int verConfig = configDbPrefs.getInt(KEY_VER_CONFIG, 1);

        return new ParametrosInfo(test, testPc, entorno, devBancardUrl, devBancardPublicKey,
                devBancardPrivateKey, prodBancardUrl, prodBancardPublicKey, prodBancardPrivateKey,
                devDefaultUrl, prodDefaultUrl, bancardCommerceCode, bancardCommerceBranch, appmovilDefaultUrl, verConfig);
    }



    public boolean hasParametros() {
        return !configDbPrefs.getString(KEY_PARAM_TEST, "").isEmpty() ||
               !configDbPrefs.getString(KEY_PARAM_ENTORNO, "").isEmpty();
    }

    public void clearParametros() {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        editor.remove(KEY_PARAM_TEST);
        editor.remove(KEY_PARAM_TESTPC);
        editor.remove(KEY_PARAM_ENTORNO);
        editor.remove(KEY_PARAM_DEV_BANCARD_URL);
        editor.remove(KEY_PARAM_DEV_BANCARD_PUBLIC_KEY);
        editor.remove(KEY_PARAM_DEV_BANCARD_PRIVATE_KEY);
        editor.remove(KEY_PARAM_PROD_BANCARD_URL);
        editor.remove(KEY_PARAM_PROD_BANCARD_PUBLIC_KEY);
        editor.remove(KEY_PARAM_PROD_BANCARD_PRIVATE_KEY);
        editor.remove(KEY_PARAM_DEV_DEFAULT_URL);
        editor.remove(KEY_PARAM_PROD_DEFAULT_URL);
        editor.remove(KEY_PARAM_BANCARD_COMMERCE_CODE);
        editor.remove(KEY_PARAM_BANCARD_COMMERCE_BRANCH);
        editor.remove(KEY_PARAM_APPMOVIL_DEFAULT_URL);
        editor.remove(KEY_VER_CONFIG);
        editor.apply();
    }

    public int getVerConfig() {
        return configDbPrefs.getInt(KEY_VER_CONFIG, 1);
    }

    // ========== MÉTODOS PARA CONFIGURACIONES SINCRONIZADAS ==========


    public void saveSyncedConfiguraciones(java.util.List<ConfiguracionSincronizada> configuraciones) {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        clearSyncedConfiguraciones();
        editor.putInt(KEY_SYNC_CONFIG_COUNT, configuraciones.size());
        for (int i = 0; i < configuraciones.size(); i++) {
            ConfiguracionSincronizada config = configuraciones.get(i);
            String prefix = KEY_SYNC_CONFIG_PREFIX + i + "_";
            editor.putInt(prefix + "id", config.getId());
            editor.putString(prefix + "urlapi", config.getUrlapi());
            editor.putString(prefix + "usuario", config.getUsuario());
            editor.putString(prefix + "clave", config.getClave());
            editor.putString(prefix + "granttype", config.getGranttype());
            editor.putString(prefix + "codempresa", config.getCodempresa());
            editor.putString(prefix + "codsucursal", config.getCodsucursal());
            editor.putString(prefix + "urlapi2", config.getUrlapi2());
            editor.putString(prefix + "usuario2", config.getUsuario2());
            editor.putString(prefix + "clave2", config.getClave2());
            editor.putString(prefix + "granttype2", config.getGranttype2());
            editor.putString(prefix + "codempresa2", config.getCodempresa2());
            editor.putString(prefix + "codsucursal2", config.getCodsucursal2());
            editor.putInt(prefix + "id_puntoventa", config.getIdPuntoventa());
        }
        editor.apply();
    }

    public java.util.List<ConfiguracionSincronizada> getSyncedConfiguraciones() {
        java.util.List<ConfiguracionSincronizada> configuraciones = new java.util.ArrayList<>();
        int count = configDbPrefs.getInt(KEY_SYNC_CONFIG_COUNT, 0);

        for (int i = 0; i < count; i++) {
            String prefix = KEY_SYNC_CONFIG_PREFIX + i + "_";
            if (configDbPrefs.contains(prefix + "id")) {
                ConfiguracionSincronizada config = new ConfiguracionSincronizada(
                    configDbPrefs.getInt(prefix + "id", 0),
                    configDbPrefs.getString(prefix + "urlapi", ""),
                    configDbPrefs.getString(prefix + "usuario", ""),
                    configDbPrefs.getString(prefix + "clave", ""),
                    configDbPrefs.getString(prefix + "granttype", ""),
                    configDbPrefs.getString(prefix + "codempresa", ""),
                    configDbPrefs.getString(prefix + "codsucursal", ""),
                    configDbPrefs.getString(prefix + "urlapi2", ""),
                    configDbPrefs.getString(prefix + "usuario2", ""),
                    configDbPrefs.getString(prefix + "clave2", ""),
                    configDbPrefs.getString(prefix + "granttype2", ""),
                    configDbPrefs.getString(prefix + "codempresa2", ""),
                    configDbPrefs.getString(prefix + "codsucursal2", ""),
                    configDbPrefs.getInt(prefix + "id_puntoventa", 0)
                );
                configuraciones.add(config);
            }
        }
        return configuraciones;
    }

    public void clearSyncedConfiguraciones() {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        int count = configDbPrefs.getInt(KEY_SYNC_CONFIG_COUNT, 0);
        for (int i = 0; i < count; i++) {
            String prefix = KEY_SYNC_CONFIG_PREFIX + i + "_";
            editor.remove(prefix + "id");
            editor.remove(prefix + "urlapi");
            editor.remove(prefix + "usuario");
            editor.remove(prefix + "clave");
            editor.remove(prefix + "granttype");
            editor.remove(prefix + "codempresa");
            editor.remove(prefix + "codsucursal");
            editor.remove(prefix + "urlapi2");
            editor.remove(prefix + "usuario2");
            editor.remove(prefix + "clave2");
            editor.remove(prefix + "granttype2");
            editor.remove(prefix + "codempresa2");
            editor.remove(prefix + "codsucursal2");
            editor.remove(prefix + "id_puntoventa");
        }
        editor.remove(KEY_SYNC_CONFIG_COUNT);
        editor.apply();
    }

    // ========== MÉTODOS PARA VENDEDORES SINCRONIZADOS ==========

    public void saveSyncedVendedores(java.util.List<VendedorSincronizado> vendedores) {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        clearSyncedVendedores();
        editor.putInt(KEY_SYNC_VENDEDOR_COUNT, vendedores.size());
        for (int i = 0; i < vendedores.size(); i++) {
            VendedorSincronizado vendedor = vendedores.get(i);
            String prefix = KEY_SYNC_VENDEDOR_PREFIX + i + "_";
            editor.putString(prefix + "codigo", vendedor.getCodigo());
            editor.putString(prefix + "nombre", vendedor.getNombre());
            editor.putString(prefix + "soloconsulta", vendedor.getSoloconsulta());
        }
        editor.apply();
    }

    public java.util.List<VendedorSincronizado> getSyncedVendedores() {
        java.util.List<VendedorSincronizado> vendedores = new java.util.ArrayList<>();
        int count = configDbPrefs.getInt(KEY_SYNC_VENDEDOR_COUNT, 0);

        for (int i = 0; i < count; i++) {
            String prefix = KEY_SYNC_VENDEDOR_PREFIX + i + "_";
            if (configDbPrefs.contains(prefix + "codigo")) {
                VendedorSincronizado vendedor = new VendedorSincronizado(
                    configDbPrefs.getString(prefix + "codigo", ""),
                    configDbPrefs.getString(prefix + "nombre", ""),
                    configDbPrefs.getString(prefix + "soloconsulta", "N")
                );
                vendedores.add(vendedor);
            }
        }
        return vendedores;
    }

    public void clearSyncedVendedores() {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        int count = configDbPrefs.getInt(KEY_SYNC_VENDEDOR_COUNT, 0);
        for (int i = 0; i < count; i++) {
            String prefix = KEY_SYNC_VENDEDOR_PREFIX + i + "_";
            editor.remove(prefix + "codigo");
            editor.remove(prefix + "nombre");
            editor.remove(prefix + "soloconsulta");
        }
        editor.remove(KEY_SYNC_VENDEDOR_COUNT);
        editor.apply();
    }

    // ========== MÉTODOS PARA PUNTOS DE VENTA SINCRONIZADOS ==========

    public void saveSyncedPuntosVenta(java.util.List<PuntoVentaSincronizado> puntosVenta) {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        clearSyncedPuntosVenta();
        editor.putInt(KEY_SYNC_PV_COUNT, puntosVenta.size());
        for (int i = 0; i < puntosVenta.size(); i++) {
            PuntoVentaSincronizado pv = puntosVenta.get(i);
            String prefix = KEY_SYNC_PV_PREFIX + i + "_";
            editor.putInt(prefix + "id", pv.getId());
            editor.putString(prefix + "nombre", pv.getNombre());
            editor.putString(prefix + "prefijo", pv.getPrefijo());
        }
        editor.apply();
    }

    public java.util.List<PuntoVentaSincronizado> getSyncedPuntosVenta() {
        java.util.List<PuntoVentaSincronizado> puntosVenta = new java.util.ArrayList<>();
        int count = configDbPrefs.getInt(KEY_SYNC_PV_COUNT, 0);

        for (int i = 0; i < count; i++) {
            String prefix = KEY_SYNC_PV_PREFIX + i + "_";
            if (configDbPrefs.contains(prefix + "id")) {
                PuntoVentaSincronizado pv = new PuntoVentaSincronizado(
                    configDbPrefs.getInt(prefix + "id", 0),
                    configDbPrefs.getString(prefix + "nombre", ""),
                    configDbPrefs.getString(prefix + "prefijo", "")
                );
                puntosVenta.add(pv);
            }
        }
        return puntosVenta;
    }

    public void clearSyncedPuntosVenta() {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        int count = configDbPrefs.getInt(KEY_SYNC_PV_COUNT, 0);
        for (int i = 0; i < count; i++) {
            String prefix = KEY_SYNC_PV_PREFIX + i + "_";
            editor.remove(prefix + "id");
            editor.remove(prefix + "nombre");
            editor.remove(prefix + "prefijo");
        }
        editor.remove(KEY_SYNC_PV_COUNT);
        editor.apply();
    }

    // ========== MÉTODOS UTILITARIOS GENERALES ==========

    public void clearAllConfigData() {
        android.content.SharedPreferences.Editor editor = configDbPrefs.edit();
        // Comentado para preservar credenciales en logout
        // editor.remove(KEY_USUARIO_SQL);
        // editor.remove(KEY_PASSWORD_SQL);
        // editor.remove(KEY_RECORDAR);
        editor.remove(KEY_ID_PUNTO_VENTA);
        editor.remove(KEY_NOMBRE_PUNTO_VENTA);
        editor.remove(KEY_PREFIJO_PUNTO_VENTA);
        editor.remove(KEY_CODIGO_VENDEDOR);
        editor.remove(KEY_SOLO_CONSULTA);
        editor.apply();
    }

    public void clearAllTokens() {
        android.content.SharedPreferences.Editor editor = appPrefs.edit();
        editor.clear();
        editor.apply();
    }

    public void clearSessionData() {
        clearAllTokens();
        clearAllConfigData();
        clearSucursalInfo();
        clearParametros();
        clearSyncedConfiguraciones();
        clearSyncedVendedores();
        clearSyncedPuntosVenta();
    }

    public boolean hasValidSession() {
        return isTokenValid() && getPuntoVentaInfo().getId() != -1;
    }

    public boolean hasSyncedData() {
        return hasParametros() ||
               configDbPrefs.getInt(KEY_SYNC_CONFIG_COUNT, 0) > 0 ||
               configDbPrefs.getInt(KEY_SYNC_VENDEDOR_COUNT, 0) > 0 ||
               configDbPrefs.getInt(KEY_SYNC_PV_COUNT, 0) > 0;
    }

    public SyncedDataInfo getSyncedDataInfo() {
        int configCount = configDbPrefs.getInt(KEY_SYNC_CONFIG_COUNT, 0);
        int vendedorCount = configDbPrefs.getInt(KEY_SYNC_VENDEDOR_COUNT, 0);
        int pvCount = configDbPrefs.getInt(KEY_SYNC_PV_COUNT, 0);
        boolean hasParametros = hasParametros();

        return new SyncedDataInfo(configCount, vendedorCount, pvCount, hasParametros);
    }

    // ========== CLASES DE DATOS ==========

    public static class UserCredentials {
        private final String usuario;
        private final String password;
        private final boolean recordar;

        public UserCredentials(String usuario, String password, boolean recordar) {
            this.usuario = usuario;
            this.password = password;
            this.recordar = recordar;
        }

        public String getUsuario() { return usuario; }
        public String getPassword() { return password; }
        public boolean isRecordar() { return recordar; }
    }

    public static class PuntoVentaInfo {
        private final int id;
        private final String nombre;
        private final String prefijo;

        public PuntoVentaInfo(int id, String nombre, String prefijo) {
            this.id = id;
            this.nombre = nombre;
            this.prefijo = prefijo;
        }

        public int getId() { return id; }
        public String getNombre() { return nombre; }
        public String getPrefijo() { return prefijo; }
    }

    public static class VendedorInfo {
        private final String codigo;
        private final String soloConsulta;

        public VendedorInfo(String codigo, String soloConsulta) {
            this.codigo = codigo;
            this.soloConsulta = soloConsulta;
        }

        public String getCodigo() { return codigo; }
        public String getSoloConsulta() { return soloConsulta; }
        public boolean isSoloConsulta() { return "S".equals(soloConsulta); }
    }


    public static class SucursalInfo {
        private final String nombre;
        private final String domicilio;
        private final String domicilio2;
        private final String localidad;
        private final String telefono;
        private final int codsuc;

        public SucursalInfo(String nombre, String domicilio, String domicilio2,
                           String localidad, String telefono, int codsuc) {
            this.nombre = nombre;
            this.domicilio = domicilio;
            this.domicilio2 = domicilio2;
            this.localidad = localidad;
            this.telefono = telefono;
            this.codsuc = codsuc;
        }

        public String getNombre() { return nombre; }
        public String getDomicilio() { return domicilio; }
        public String getDomicilio2() { return domicilio2; }
        public String getLocalidad() { return localidad; }
        public String getTelefono() { return telefono; }
        public int getCodsuc() { return codsuc; }

        public String getDireccionCompleta() {
            String direccion = domicilio;
            if (domicilio2 != null && !domicilio2.isEmpty()) {
                direccion += ", " + domicilio2;
            }
            return direccion;
        }
    }

    public static class ParametrosInfo {
        private final String test;
        private final String testPc;
        private final String entorno;
        private final String devBancardUrl;
        private final String devBancardPublicKey;
        private final String devBancardPrivateKey;
        private final String prodBancardUrl;
        private final String prodBancardPublicKey;
        private final String prodBancardPrivateKey;
        private final String devDefaultUrl;
        private final String prodDefaultUrl;
        private final int bancardCommerceCode;
        private final int bancardCommerceBranch;
        private final String appmovilDefaultUrl;
        private final int verConfig;

        public ParametrosInfo(String test, String testPc, String entorno, String devBancardUrl,
                             String devBancardPublicKey, String devBancardPrivateKey,
                             String prodBancardUrl, String prodBancardPublicKey, String prodBancardPrivateKey,
                             String devDefaultUrl, String prodDefaultUrl, int bancardCommerceCode,
                             int bancardCommerceBranch, String appmovilDefaultUrl, int verConfig) {
            this.test = test;
            this.testPc = testPc;
            this.entorno = entorno;
            this.devBancardUrl = devBancardUrl;
            this.devBancardPublicKey = devBancardPublicKey;
            this.devBancardPrivateKey = devBancardPrivateKey;
            this.prodBancardUrl = prodBancardUrl;
            this.prodBancardPublicKey = prodBancardPublicKey;
            this.prodBancardPrivateKey = prodBancardPrivateKey;
            this.devDefaultUrl = devDefaultUrl;
            this.prodDefaultUrl = prodDefaultUrl;
            this.bancardCommerceCode = bancardCommerceCode;
            this.bancardCommerceBranch = bancardCommerceBranch;
            this.appmovilDefaultUrl = appmovilDefaultUrl;
            this.verConfig = verConfig;
        }

        public String getTest() { return test; }
        public String getTestPc() { return testPc; }
        public String getEntorno() { return entorno; }
        public String getDevBancardUrl() { return devBancardUrl; }
        public String getDevBancardPublicKey() { return devBancardPublicKey; }
        public String getDevBancardPrivateKey() { return devBancardPrivateKey; }
        public String getProdBancardUrl() { return prodBancardUrl; }
        public String getProdBancardPublicKey() { return prodBancardPublicKey; }
        public String getProdBancardPrivateKey() { return prodBancardPrivateKey; }
        public String getDevDefaultUrl() { return devDefaultUrl; }
        public String getProdDefaultUrl() { return prodDefaultUrl; }
        public int getBancardCommerceCode() { return bancardCommerceCode; }
        public int getBancardCommerceBranch() { return bancardCommerceBranch; }
        public String getAppmovilDefaultUrl() { return appmovilDefaultUrl; }
        public int getVerConfig() { return verConfig; }
    }

    public static class ConfiguracionSincronizada {
        private final int id;
        private final String urlapi;
        private final String usuario;
        private final String clave;
        private final String granttype;
        private final String codempresa;
        private final String codsucursal;
        private final String urlapi2;
        private final String usuario2;
        private final String clave2;
        private final String granttype2;
        private final String codempresa2;
        private final String codsucursal2;
        private final int idPuntoventa;

        public ConfiguracionSincronizada(int id, String urlapi, String usuario, String clave,
                                       String granttype, String codempresa, String codsucursal,
                                       String urlapi2, String usuario2, String clave2,
                                       String granttype2, String codempresa2, String codsucursal2,
                                       int idPuntoventa) {
            this.id = id;
            this.urlapi = urlapi;
            this.usuario = usuario;
            this.clave = clave;
            this.granttype = granttype;
            this.codempresa = codempresa;
            this.codsucursal = codsucursal;
            this.urlapi2 = urlapi2;
            this.usuario2 = usuario2;
            this.clave2 = clave2;
            this.granttype2 = granttype2;
            this.codempresa2 = codempresa2;
            this.codsucursal2 = codsucursal2;
            this.idPuntoventa = idPuntoventa;
        }

        public int getId() { return id; }
        public String getUrlapi() { return urlapi; }
        public String getUsuario() { return usuario; }
        public String getClave() { return clave; }
        public String getGranttype() { return granttype; }
        public String getCodempresa() { return codempresa; }
        public String getCodsucursal() { return codsucursal; }
        public String getUrlapi2() { return urlapi2; }
        public String getUsuario2() { return usuario2; }
        public String getClave2() { return clave2; }
        public String getGranttype2() { return granttype2; }
        public String getCodempresa2() { return codempresa2; }
        public String getCodsucursal2() { return codsucursal2; }
        public int getIdPuntoventa() { return idPuntoventa; }
    }

    public static class VendedorSincronizado {
        private final String codigo;
        private final String nombre;
        private final String soloconsulta;

        public VendedorSincronizado(String codigo, String nombre, String soloconsulta) {
            this.codigo = codigo;
            this.nombre = nombre;
            this.soloconsulta = soloconsulta;
        }

        public String getCodigo() { return codigo; }
        public String getNombre() { return nombre; }
        public String getSoloconsulta() { return soloconsulta; }
        public boolean isSoloConsulta() { return "S".equals(soloconsulta); }
    }

    public static class PuntoVentaSincronizado {
        private final int id;
        private final String nombre;
        private final String prefijo;

        public PuntoVentaSincronizado(int id, String nombre, String prefijo) {
            this.id = id;
            this.nombre = nombre;
            this.prefijo = prefijo;
        }

        public int getId() { return id; }
        public String getNombre() { return nombre; }
        public String getPrefijo() { return prefijo; }
    }

    public static class SyncedDataInfo {
        private final int configCount;
        private final int vendedorCount;
        private final int pvCount;
        private final boolean hasParametros;

        public SyncedDataInfo(int configCount, int vendedorCount, int pvCount, boolean hasParametros) {
            this.configCount = configCount;
            this.vendedorCount = vendedorCount;
            this.pvCount = pvCount;
            this.hasParametros = hasParametros;
        }

        public int getConfigCount() { return configCount; }
        public int getVendedorCount() { return vendedorCount; }
        public int getPvCount() { return pvCount; }
        public boolean hasParametros() { return hasParametros; }

        public String getSummary() {
            return String.format("Configs: %d, Vendedores: %d, PVs: %d, Parámetros: %s",
                configCount, vendedorCount, pvCount, hasParametros ? "Sí" : "No");
        }
    }
}
